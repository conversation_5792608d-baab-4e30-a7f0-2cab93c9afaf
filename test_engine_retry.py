#!/usr/bin/env python3
"""
Test script to verify the engine retry logic for page number validation.
This creates a mock scenario to test the retry behavior.
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock
from staccato.llm.validation import Event, LLMResponse
from staccato.core.engine import <PERSON><PERSON><PERSON>ng<PERSON>
from staccato.config import ChunkingEngineConfig, LLMConfig, RetryConfig, PreprocessingConfig


class MockLLMAdapter:
    """Mock LLM adapter that can simulate invalid page number responses."""
    
    def __init__(self):
        self.call_count = 0
        self.responses = []
        
    def set_responses(self, responses):
        """Set the sequence of responses to return."""
        self.responses = responses
        self.call_count = 0
        
    async def agenerate_and_validate(self, system_prompt, user_prompt, **kwargs):
        """Mock implementation that returns predefined responses."""
        if self.call_count < len(self.responses):
            response = self.responses[self.call_count]
            self.call_count += 1
            
            # Check if this is a corrected prompt (contains correction text)
            if "IMPORTANT CORRECTION" in user_prompt:
                print(f"📝 Retry attempt {self.call_count} with correction detected")
                print(f"   Correction text found in prompt: {'IMPORTANT CORRECTION' in user_prompt}")
            
            return response
        else:
            # Fallback to valid response if we run out of predefined responses
            return LLMResponse(events=[
                Event(event="STARTS", level="section", page_number=1, title="Valid Section", fingerprint="valid text")
            ])


async def test_retry_logic():
    """Test that the engine retries when LLM returns invalid page numbers."""
    print("🧪 Testing engine retry logic for invalid page numbers...")
    
    # Create mock responses: first invalid, then valid
    invalid_response = LLMResponse(events=[
        Event(event="STARTS", level="section", page_number=99, title="Invalid Section", fingerprint="text1"),  # Invalid page
    ])
    
    valid_response = LLMResponse(events=[
        Event(event="STARTS", level="section", page_number=1, title="Valid Section", fingerprint="text1"),
    ])
    
    # Create mock adapter
    mock_adapter = MockLLMAdapter()
    mock_adapter.set_responses([invalid_response, valid_response])
    
    # Create engine with mock adapter
    config = ChunkingEngineConfig(
        llm=LLMConfig(provider="openai", model_name="gpt-4"),
        retry=RetryConfig(page_validation_attempts=3),
        preprocessing=PreprocessingConfig(page_batch_size=1)
    )
    
    engine = ChunkingEngine(config=config)
    
    # Replace the LLM adapter with our mock
    engine.llm_adapter = mock_adapter
    
    # Mock the preprocessor and other dependencies
    mock_preprocessor = MagicMock()
    mock_page = MagicMock()
    mock_page.text = "Sample page content"
    mock_preprocessor.extract_pages.return_value = [mock_page]
    
    # Mock the get_preprocessor function
    import staccato.core.engine as engine_module
    original_get_preprocessor = engine_module.get_preprocessor
    engine_module.get_preprocessor = MagicMock(return_value=mock_preprocessor)
    
    try:
        # This should trigger the retry logic
        print("📄 Processing mock document...")
        chunks = await engine.aprocess_document("mock_document.pdf")
        
        print(f"✅ Success! Generated {len(chunks)} chunks")
        print(f"📊 LLM adapter was called {mock_adapter.call_count} times")
        
        # Verify that retry happened
        assert mock_adapter.call_count == 2, f"Expected 2 calls (1 invalid + 1 retry), got {mock_adapter.call_count}"
        print("✅ Retry logic worked correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Restore original function
        engine_module.get_preprocessor = original_get_preprocessor


async def test_max_retries_exceeded():
    """Test that the engine fails after max retries are exceeded."""
    print("\n🧪 Testing max retries exceeded scenario...")
    
    # Create multiple invalid responses (more than max retries)
    invalid_response = LLMResponse(events=[
        Event(event="STARTS", level="section", page_number=99, title="Invalid Section", fingerprint="text1"),
    ])
    
    mock_adapter = MockLLMAdapter()
    mock_adapter.set_responses([invalid_response, invalid_response, invalid_response, invalid_response])
    
    config = ChunkingEngineConfig(
        llm=LLMConfig(provider="openai", model_name="gpt-4"),
        retry=RetryConfig(page_validation_attempts=2),  # Only 2 attempts
        preprocessing=PreprocessingConfig(page_batch_size=1)
    )
    
    engine = ChunkingEngine(config=config)
    engine.llm_adapter = mock_adapter
    
    # Mock dependencies
    mock_preprocessor = MagicMock()
    mock_page = MagicMock()
    mock_page.text = "Sample page content"
    mock_preprocessor.extract_pages.return_value = [mock_page]
    
    import staccato.core.engine as engine_module
    original_get_preprocessor = engine_module.get_preprocessor
    engine_module.get_preprocessor = MagicMock(return_value=mock_preprocessor)
    
    try:
        await engine.aprocess_document("mock_document.pdf")
        print("❌ Expected PageNumberValidationError but none was raised")
    except Exception as e:
        if "invalid page numbers" in str(e):
            print(f"✅ Correctly failed after max retries: {e}")
            print(f"📊 LLM adapter was called {mock_adapter.call_count} times")
        else:
            print(f"❌ Unexpected error: {e}")
    finally:
        engine_module.get_preprocessor = original_get_preprocessor


async def main():
    """Run all tests."""
    await test_retry_logic()
    await test_max_retries_exceeded()
    print("\n🎉 All engine retry tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
