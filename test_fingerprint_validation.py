#!/usr/bin/env python3
"""
Test script to verify fingerprint validation functionality.
"""

import pytest
from staccato.llm.validation import Event, LLMResponse, validate_fingerprints, FingerprintValidationError


def test_valid_fingerprints():
    """Test that validation passes with valid fingerprints."""
    page_content = """This is the introduction to safety procedures. All employees must follow these guidelines carefully.

Safety procedures are complete.

1. Turn off all equipment immediately
2. Evacuate the building
3. Call emergency services"""

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Safety", fingerprint="This is the introduction"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="procedures are complete."),
        Event(event="STARTS", level="list", page_number=1, title="Steps", fingerprint="1. Turn off"),
    ]
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Should not raise any exception
    validate_fingerprints(response, page_content_map)
    print("✅ Valid fingerprints passed validation")


def test_missing_fingerprints():
    """Test that validation fails with missing fingerprints."""
    page_content = """This is the introduction to safety procedures. All employees must follow these guidelines carefully.

Safety procedures are complete."""

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Safety", fingerprint="This is the introduction"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="procedures are complete."),
        Event(event="STARTS", level="list", page_number=1, title="Steps", fingerprint="1. Turn off"),  # Not in content
    ]
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    with pytest.raises(FingerprintValidationError) as exc_info:
        validate_fingerprints(response, page_content_map)
    
    error = exc_info.value
    assert len(error.missing_fingerprints) == 1
    assert error.missing_fingerprints[0]["fingerprint"] == "1. Turn off"
    assert error.page_number == 1
    print(f"✅ Correctly caught missing fingerprint: {error.missing_fingerprints[0]['fingerprint']}")


def test_multiple_missing_fingerprints():
    """Test validation with multiple missing fingerprints."""
    page_content = """This is some content on the page."""

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section", fingerprint="This is some"),  # Valid
        Event(event="ENDS", level="section", page_number=1, fingerprint="not found text"),  # Invalid
        Event(event="STARTS", level="list", page_number=1, title="List", fingerprint="also missing"),  # Invalid
    ]
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    with pytest.raises(FingerprintValidationError) as exc_info:
        validate_fingerprints(response, page_content_map)
    
    error = exc_info.value
    assert len(error.missing_fingerprints) == 2
    missing_texts = [item["fingerprint"] for item in error.missing_fingerprints]
    assert "not found text" in missing_texts
    assert "also missing" in missing_texts
    print(f"✅ Correctly caught multiple missing fingerprints: {missing_texts}")


def test_whitespace_handling():
    """Test that fingerprints work with whitespace differences."""
    page_content = """This is the introduction to safety procedures.
    
    Safety procedures are complete.
    
    1. Turn off all equipment"""

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Safety", fingerprint="This is the introduction"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="Safety procedures are complete."),  # Should work despite indentation
        Event(event="STARTS", level="list", page_number=1, title="Steps", fingerprint="1. Turn off"),  # Should work despite indentation
    ]
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Should not raise any exception
    validate_fingerprints(response, page_content_map)
    print("✅ Whitespace handling works correctly")


def test_continuation_events_skipped():
    """Test that CONTINUATION events are skipped in validation."""
    page_content = """Some content here."""

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section", fingerprint="Some content"),
        Event(event="CONTINUATION", level="section", page_number=1),  # No fingerprint needed
    ]
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Should not raise any exception
    validate_fingerprints(response, page_content_map)
    print("✅ CONTINUATION events correctly skipped")


def test_empty_fingerprints_skipped():
    """Test that events with empty fingerprints are skipped."""
    page_content = """Some content here."""

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section", fingerprint="Some content"),
        Event(event="ENDS", level="section", page_number=1, fingerprint=""),  # Empty fingerprint
    ]
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Should not raise any exception
    validate_fingerprints(response, page_content_map)
    print("✅ Empty fingerprints correctly skipped")


def test_multiple_pages():
    """Test fingerprint validation across multiple pages."""
    page_content_map = {
        1: "Content on page one with some text.",
        2: "Content on page two with different text.",
        3: "Content on page three with more text."
    }

    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section1", fingerprint="Content on page one"),
        Event(event="ENDS", level="section", page_number=2, fingerprint="different text."),
        Event(event="STARTS", level="section", page_number=3, title="Section2", fingerprint="missing text"),  # Not found
    ]
    
    response = LLMResponse(events=events)
    
    with pytest.raises(FingerprintValidationError) as exc_info:
        validate_fingerprints(response, page_content_map)
    
    error = exc_info.value
    assert len(error.missing_fingerprints) == 1
    assert error.missing_fingerprints[0]["fingerprint"] == "missing text"
    assert error.missing_fingerprints[0]["page_number"] == 3
    print(f"✅ Multi-page validation works correctly")


def main():
    """Run all fingerprint validation tests."""
    print("🚀 Testing fingerprint validation functionality...")
    
    try:
        test_valid_fingerprints()
        test_missing_fingerprints()
        test_multiple_missing_fingerprints()
        test_whitespace_handling()
        test_continuation_events_skipped()
        test_empty_fingerprints_skipped()
        test_multiple_pages()
        
        print("\n🎉 All fingerprint validation tests passed!")
        print("\n📋 Summary of what was tested:")
        print("   ✅ Valid fingerprints pass validation")
        print("   ✅ Missing fingerprints are correctly identified")
        print("   ✅ Multiple missing fingerprints are handled")
        print("   ✅ Whitespace differences are handled gracefully")
        print("   ✅ CONTINUATION events are skipped")
        print("   ✅ Empty fingerprints are skipped")
        print("   ✅ Multi-page validation works correctly")
        print("\n🔧 The engine retry logic should now handle fingerprint errors!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
