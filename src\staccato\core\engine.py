import os
import json
import random
import string
from typing import List, Dict
from ..config import ChunkingEngineConfig
from ..llm import get_llm_adapter
from ..llm.validation import validate_page_numbers, PageNumberValidationError, validate_fingerprints, FingerprintValidationError
from ..preprocess.factory import get_preprocessor
from ..preprocess.markup import convert_page_to_markdown
from .stitcher import StatefulStitcher
from .assembler import FinalAssembler
from ..models import Chunk
from ..utils.logging import get_logger
from ..internal.prompts import SYSTEM_PROMPT, USER_PROMPT_TEMPLATE

logger = get_logger(__name__)

class ChunkingEngine:
    def __init__(self, config: ChunkingEngineConfig = None):
        """
        Initializes the ChunkingEngine.

        Args:
            config: An optional ChunkingEngineConfig object. If not provided,
                    configuration is loaded automatically from 'staccato.toml'
                    and environment variables.
        """
        self.config = config or ChunkingEngineConfig()
        self.llm_adapter = get_llm_adapter(self.config)
        logger.info(f"ChunkingEngine initialized with LLM provider: {self.config.llm.provider}")
        logger.debug("Full configuration", config=self.config.model_dump())

    def _generate_page_id(self, page_number: int) -> str:
        """
        Generate a 4-digit alphanumeric page ID to avoid confusion with page numbers.

        Args:
            page_number: The actual page number

        Returns:
            A 4-character alphanumeric ID (e.g., 'A1B2', 'X9Y4')
        """
        # Use a deterministic approach based on page number to ensure consistency
        # This ensures the same page number always gets the same ID
        random.seed(page_number * 12345)  # Use a seed based on page number

        # Generate 4 characters: alternating letter-digit pattern
        chars = []
        for i in range(4):
            if i % 2 == 0:  # Even positions: letters
                chars.append(random.choice(string.ascii_uppercase))
            else:  # Odd positions: digits
                chars.append(random.choice(string.digits))

        return ''.join(chars)

    def _validate_page_ids(self, llm_response, page_id_reverse_map: Dict[str, int]) -> None:
        """
        Validate that all page IDs in the LLM response are valid alphanumeric IDs.

        Args:
            llm_response: The LLM response containing events
            page_id_reverse_map: Dictionary mapping page IDs to page numbers

        Raises:
            PageNumberValidationError: If any page IDs are invalid
        """
        valid_page_ids = set(page_id_reverse_map.keys())
        invalid_page_ids = []

        for event in llm_response.events:
            if event.page_id not in valid_page_ids:
                invalid_page_ids.append(event.page_id)

        if invalid_page_ids:
            invalid_page_ids = sorted(list(set(invalid_page_ids)))  # Remove duplicates and sort
            valid_ids_list = sorted(list(valid_page_ids))
            message = (
                f"LLM response contains invalid page IDs: {invalid_page_ids}. "
                f"Valid page IDs are: {valid_ids_list}."
            )
            # Convert to page numbers for the exception (for compatibility)
            invalid_page_numbers = [page_id_reverse_map.get(pid, -1) for pid in invalid_page_ids]
            valid_page_numbers = [page_id_reverse_map[pid] for pid in valid_ids_list]
            min_page = min(valid_page_numbers)
            max_page = max(valid_page_numbers)
            raise PageNumberValidationError(message, invalid_page_numbers, (min_page, max_page))

    def _convert_page_ids_to_numbers(self, llm_response, page_id_reverse_map: Dict[str, int]):
        """
        Convert page IDs back to page numbers in the LLM response.

        Args:
            llm_response: The LLM response with page IDs
            page_id_reverse_map: Dictionary mapping page IDs to page numbers

        Returns:
            A new LLM response with page numbers instead of page IDs
        """
        from ..llm.validation import Event, LLMResponse

        converted_events = []
        for event in llm_response.events:
            # Convert page_id back to page_number for stitcher compatibility
            page_number = page_id_reverse_map.get(event.page_id, event.page_id)

            # Create new event with page_number instead of page_id
            event_dict = event.model_dump()
            event_dict['page_id'] = page_number  # The stitcher expects page_id field
            converted_events.append(Event(**event_dict))

        return LLMResponse(events=converted_events)

    def process_document(self, document_path: str) -> List[Chunk]:
        """Processes a single document and returns a list of semantic chunks."""
        # This synchronous method is not the primary focus and is not implemented
        # with the full batching and async capabilities.
        # It remains for simple use cases or environments where async is not feasible.
        raise NotImplementedError("Please use the 'aprocess_document' method for full functionality.")

    async def aprocess_document(self, document_path: str) -> List[Chunk]:
        """Asynchronously processes a single document."""
        logger.info("Async processing document", path=document_path)

        preprocessor = get_preprocessor(document_path, self.config.preprocessing)
        pages = preprocessor.extract_pages(document_path)
        logger.info(f"Extracted {len(pages)} pages.")

        stitcher = StatefulStitcher()
        batch_size = self.config.preprocessing.page_batch_size

        for i in range(0, len(pages), batch_size):
            batch_pages = pages[i:i + batch_size]
            page_numbers = list(range(i + 1, i + len(batch_pages) + 1))
            logger.info(f"\n📄 Processing page batch {i//batch_size + 1}: pages {page_numbers[0]}-{page_numbers[-1]}")

            # Create a map of page number to page content for the stitcher
            page_content_map = {
                page_num: convert_page_to_markdown(p) if self.config.preprocessing.use_layout_analysis else p.text
                for page_num, p in zip(page_numbers, batch_pages)
            }

            # Create mapping between page numbers and alphanumeric IDs
            page_id_map = {}  # page_number -> page_id
            page_id_reverse_map = {}  # page_id -> page_number

            for page_num in page_numbers:
                page_id = self._generate_page_id(page_num)
                page_id_map[page_num] = page_id
                page_id_reverse_map[page_id] = page_num

            logger.info(f"Generated page ID mapping: {page_id_map}")

            # Combine page content for the batch, using alphanumeric page IDs
            page_contents = []
            for page_num, content in page_content_map.items():
                page_id = page_id_map[page_num]
                page_header = f"THIS IS PAGE ID {page_id}"
                separator_line = "=" * len(page_header)
                page_contents.append(f"{separator_line}\n{page_header}\n{separator_line}\n\n{content}")

            combined_page_content = "\n\n\n".join(page_contents)

            user_prompt = self._construct_user_prompt(combined_page_content, stitcher.active_stack, page_numbers)

            # Retry logic for page number validation
            max_page_validation_retries = self.config.retry.page_validation_attempts
            llm_response = None

            for retry_attempt in range(max_page_validation_retries):
                try:
                    llm_response = await self.llm_adapter.agenerate_and_validate(
                        system_prompt=SYSTEM_PROMPT,
                        user_prompt=user_prompt,
                        max_tokens=self.config.llm.max_tokens,
                        temperature=self.config.llm.temperature,
                        reasoning_effort=self.config.llm.reasoning_effort
                    )

                    # Validate page IDs (convert alphanumeric IDs back to page numbers for validation)
                    self._validate_page_ids(llm_response, page_id_reverse_map)

                    # Validate fingerprints (need to map page IDs back to page numbers for content lookup)
                    page_content_map_by_id = {}
                    for page_id, page_num in page_id_reverse_map.items():
                        page_content_map_by_id[page_id] = page_content_map[page_num]
                    validate_fingerprints(llm_response, page_content_map_by_id)
                    break  # Success, exit retry loop

                except PageNumberValidationError as e:
                    logger.warning(f"Page validation failed on attempt {retry_attempt + 1}: {e}")

                    if retry_attempt == max_page_validation_retries - 1:
                        logger.error(f"Page validation failed after {max_page_validation_retries} attempts")
                        raise

                    # Add corrective instructions to the user prompt for retry
                    valid_page_ids = list(page_id_reverse_map.keys())
                    correction_text = (
                        f"\n\nIMPORTANT CORRECTION: Your previous response contained invalid page ids: {e.invalid_pages}. "
                        f"You must ONLY use the following page ids: {valid_page_ids}. "
                        f"Do not reference any page ids outside this list."
                    )
                    user_prompt = user_prompt + correction_text
                    logger.info(f"Retrying with page ID correction: valid IDs are {valid_page_ids}")

                except FingerprintValidationError as e:
                    logger.warning(f"Fingerprint validation failed on attempt {retry_attempt + 1}: {e}")

                    if retry_attempt == max_page_validation_retries - 1:
                        logger.error(f"Fingerprint validation failed after {max_page_validation_retries} attempts")
                        raise

                    # Add corrective instructions for fingerprint issues
                    missing_fingerprints = [item["fingerprint"] for item in e.missing_fingerprints]
                    correction_text = (
                        f"\n\nIMPORTANT CORRECTION: Your previous response contained fingerprints that could not be found in the page content: {missing_fingerprints}. "
                        f"Please check that you are copying the exact text from the document. "
                        f"Try using fewer words (2-3 words maximum) or check for missing characters/wrong formatting wrt the source text in your fingerprints. "
                        f"The fingerprint must appear exactly as written in the page content."
                    )
                    user_prompt = user_prompt + correction_text
                    logger.info(f"Retrying with fingerprint correction for missing: {missing_fingerprints}")

            # Convert page IDs back to page numbers in the LLM response for stitcher processing
            converted_response = self._convert_page_ids_to_numbers(llm_response, page_id_reverse_map)
            stitcher.process_events(converted_response, page_content_map)
            
        completed_chunks = stitcher.finalize()
        
        assembler = FinalAssembler()
        doc_name = os.path.basename(document_path)
        final_chunks = assembler.assemble(completed_chunks, doc_name)

        logger.info(f"Successfully generated {len(final_chunks)} async chunks.")

        # Log final token usage summary
        token_summary = self.llm_adapter.get_token_usage_summary()
        logger.info(f"Document processing complete. Token usage summary: {token_summary}")

        return final_chunks

    def _construct_user_prompt(self, page_content: str, active_stack: List, page_numbers: List[int]) -> str:
        """Constructs the user prompt to send to the LLM."""
        # Create page range string
        if len(page_numbers) == 1:
            page_range = f"Page {page_numbers[0]}"
        else:
            page_range = f"Pages {page_numbers[0]}-{page_numbers[-1]}"
        
        # Create JSON representation of active blocks
        if active_stack:
            active_blocks = []
            for chunk in active_stack:
                block_info = {
                    "level": chunk.level,
                    "title": chunk.title,
                    "start_page": chunk.start_page,
                    "parent_hierarchy": chunk.parent_hierarchy
                }
                active_blocks.append(block_info)
            active_blocks_json = json.dumps(active_blocks, indent=2)
        else:
            active_blocks_json = "[]"
            
        return USER_PROMPT_TEMPLATE.format(
            page_range=page_range,
            active_blocks_json=active_blocks_json,
            page_content=page_content
        ) 