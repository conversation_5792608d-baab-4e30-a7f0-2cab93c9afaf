#!/usr/bin/env python3
"""
Test script to verify gap detection and fixing between ENDS and STARTS events.
"""

from staccato.core.stitcher import StatefulStitcher
from staccato.llm.validation import Event, LLMResponse


def test_gap_detection_and_fixing():
    """Test that gaps between ENDS and STARTS events are detected and fixed."""
    print("🧪 Testing gap detection and fixing...")
    
    stitcher = StatefulStitcher()
    
    # Simulate page content with a gap between chunks
    page_content = """Introduction to safety procedures. All employees must follow these guidelines carefully.

Safety procedures are complete.

These are additional notes that should belong to the safety section.
Some more important details here.

1. Turn off all equipment immediately
2. Evacuate the building
3. Call emergency services"""
    
    # Create events where there's a gap between ENDS and STARTS
    events = [
        Event(
            event="STARTS", 
            level="section", 
            page_number=1, 
            title="Safety Procedures", 
            fingerprint="Introduction to safety"
        ),
        Event(
            event="ENDS", 
            level="section", 
            page_number=1, 
            fingerprint="procedures are complete."  # Ends here
        ),
        Event(
            event="STARTS", 
            level="list", 
            page_number=1, 
            title="Emergency Steps", 
            fingerprint="1. Turn off"  # Starts here, leaving gap text in between
        )
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Process the events
    stitcher.process_events(llm_response, page_content_map)
    completed_chunks = stitcher.finalize()
    
    print(f"📊 Generated {len(completed_chunks)} chunks")
    
    # Check the safety procedures chunk
    safety_chunk = completed_chunks[0]
    print(f"\n📝 Safety Procedures chunk:")
    print(f"   Title: {safety_chunk.title}")
    print(f"   Text: '{safety_chunk.text_content}'")
    
    # The safety chunk should include the gap text
    expected_gap_text = "These are additional notes that should belong to the safety section.\nSome more important details here."
    
    assert "Safety procedures are complete." in safety_chunk.text_content
    assert "These are additional notes" in safety_chunk.text_content
    assert "Some more important details" in safety_chunk.text_content
    
    # Check the emergency steps chunk
    emergency_chunk = completed_chunks[1]
    print(f"\n📝 Emergency Steps chunk:")
    print(f"   Title: {emergency_chunk.title}")
    print(f"   Text: '{emergency_chunk.text_content}'")
    
    # The emergency chunk should start with the list
    assert "1. Turn off all equipment immediately" in emergency_chunk.text_content
    assert "2. Evacuate the building" in emergency_chunk.text_content
    assert "3. Call emergency services" in emergency_chunk.text_content
    
    # The gap text should NOT be in the emergency chunk
    assert "These are additional notes" not in emergency_chunk.text_content
    
    print("✅ Gap detection and fixing works correctly!")
    return True


def test_no_gap_scenario():
    """Test that contiguous fingerprints work normally without gap fixing."""
    print("\n🧪 Testing scenario with no gaps...")
    
    stitcher = StatefulStitcher()
    
    # Page content where ENDS and STARTS are contiguous
    page_content = """Section content here. Section ends here.
New section starts here. More content follows."""
    
    events = [
        Event(
            event="STARTS", 
            level="section", 
            page_number=1, 
            title="First Section", 
            fingerprint="Section content here"
        ),
        Event(
            event="ENDS", 
            level="section", 
            page_number=1, 
            fingerprint="Section ends here."
        ),
        Event(
            event="STARTS", 
            level="section", 
            page_number=1, 
            title="Second Section", 
            fingerprint="New section starts"
        )
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    stitcher.process_events(llm_response, page_content_map)
    completed_chunks = stitcher.finalize()
    
    # Check that chunks are properly separated without gap issues
    first_chunk = completed_chunks[0]
    second_chunk = completed_chunks[1]
    
    print(f"📝 First chunk: '{first_chunk.text_content}'")
    print(f"📝 Second chunk: '{second_chunk.text_content}'")
    
    assert "Section content here. Section ends here." in first_chunk.text_content
    assert "New section starts here. More content follows." in second_chunk.text_content
    
    # No overlap should occur
    assert "New section starts" not in first_chunk.text_content
    
    print("✅ No-gap scenario works correctly!")
    return True


def test_multiple_gaps():
    """Test handling of multiple gaps in sequence."""
    print("\n🧪 Testing multiple gaps scenario...")
    
    stitcher = StatefulStitcher()
    
    page_content = """First section content. First section ends.

Gap text for first section.

Second section content. Second section ends.

Gap text for second section.

Third section starts here."""
    
    events = [
        Event(event="STARTS", level="section", page_number=1, title="First", fingerprint="First section content"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="First section ends."),
        Event(event="STARTS", level="section", page_number=1, title="Second", fingerprint="Second section content"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="Second section ends."),
        Event(event="STARTS", level="section", page_number=1, title="Third", fingerprint="Third section starts"),
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    stitcher.process_events(llm_response, page_content_map)
    completed_chunks = stitcher.finalize()
    
    print(f"📊 Generated {len(completed_chunks)} chunks")
    
    # Check that each chunk includes its gap text
    first_chunk = completed_chunks[0]
    second_chunk = completed_chunks[1]
    third_chunk = completed_chunks[2]
    
    print(f"📝 First chunk: '{first_chunk.text_content}'")
    print(f"📝 Second chunk: '{second_chunk.text_content}'")
    print(f"📝 Third chunk: '{third_chunk.text_content}'")
    
    assert "Gap text for first section." in first_chunk.text_content
    assert "Gap text for second section." in second_chunk.text_content
    assert "Third section starts here." in third_chunk.text_content
    
    print("✅ Multiple gaps handled correctly!")
    return True


def test_whitespace_only_gap():
    """Test that whitespace-only gaps are not considered meaningful."""
    print("\n🧪 Testing whitespace-only gap...")
    
    stitcher = StatefulStitcher()
    
    page_content = """Section content here. Section ends here.
    
    
New section starts here."""
    
    events = [
        Event(event="STARTS", level="section", page_number=1, title="First", fingerprint="Section content here"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="Section ends here."),
        Event(event="STARTS", level="section", page_number=1, title="Second", fingerprint="New section starts"),
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    stitcher.process_events(llm_response, page_content_map)
    completed_chunks = stitcher.finalize()
    
    first_chunk = completed_chunks[0]
    second_chunk = completed_chunks[1]
    
    # Whitespace-only gaps should not trigger gap fixing
    # The chunks should be clean without extra whitespace
    assert first_chunk.text_content.strip().endswith("Section ends here.")
    assert second_chunk.text_content.strip().startswith("New section starts here.")
    
    print("✅ Whitespace-only gaps handled correctly!")
    return True


def main():
    """Run all gap detection tests."""
    print("🚀 Testing gap detection and fixing between ENDS and STARTS events")
    print("=" * 70)
    
    success = True
    
    try:
        if not test_gap_detection_and_fixing():
            success = False
    except Exception as e:
        print(f"❌ test_gap_detection_and_fixing failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_no_gap_scenario():
            success = False
    except Exception as e:
        print(f"❌ test_no_gap_scenario failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_multiple_gaps():
            success = False
    except Exception as e:
        print(f"❌ test_multiple_gaps failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_whitespace_only_gap():
            success = False
    except Exception as e:
        print(f"❌ test_whitespace_only_gap failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    if success:
        print("\n🎉 All gap detection tests passed!")
        print("\n📋 Summary of gap detection features:")
        print("   ✅ Detects text gaps between ENDS and STARTS events")
        print("   ✅ Automatically adds gap text to the ending chunk")
        print("   ✅ Handles multiple gaps in sequence")
        print("   ✅ Ignores whitespace-only gaps")
        print("   ✅ Works with contiguous fingerprints normally")
        print("\n🔧 This ensures no text is lost when LLM fingerprints aren't perfectly contiguous!")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
