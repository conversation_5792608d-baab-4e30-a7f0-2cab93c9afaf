# Markup Removal for Fingerprint Matching

## Overview

Implemented markup removal functionality to improve fingerprint matching robustness. The LLM sees rendered text without markup tags, but the underlying page content may contain HTML, Markdown, or other markup. This feature removes markup during fingerprint comparison to ensure accurate matching.

## Problem Statement

**Scenario:**
- Page content contains: `<p>This is the **introduction** to safety procedures.</p>`
- <PERSON><PERSON> sees rendered text: `This is the introduction to safety procedures.`
- LLM returns fingerprint: `introduction to safety`
- Original matching fails because `introduction to safety` is not found in `<p>This is the **introduction** to safety procedures.</p>`

**Solution:**
- Remove markup from page content during comparison
- Match fingerprint against cleaned text: `This is the introduction to safety procedures.`
- Fingerprint `introduction to safety` now matches successfully

## Implementation

### 1. Markup Removal Function (`src/staccato/llm/validation.py`)

```python
def remove_markup_tags(text: str) -> str:
    """
    Remove common markup tags from text to improve fingerprint matching.
    
    Removes HTML tags, markdown formatting, and other common markup
    that might be present in the source text but not visible to the LLM.
    """
```

**Supported Markup Types:**

**HTML Tags:**
- `<p>`, `<div>`, `<span>`, `<strong>`, `<em>`, etc.
- Nested tags: `<div><span>text</span></div>` → `text`

**Markdown Formatting:**
- Bold: `**text**` → `text`
- Italic: `*text*` → `text`
- Links: `[text](url)` → `text`
- Headers: `# Header` → `Header`
- Code blocks: `` ```code``` `` → (removed)
- Inline code: `` `code` `` → `code`

**HTML Entities:**
- `&amp;` → `&`
- `&lt;` → `<`
- `&gt;` → `>`
- `&quot;` → `"`
- `&nbsp;` → ` `

**Whitespace Cleanup:**
- Multiple spaces → single space
- Trim leading/trailing whitespace

### 2. Enhanced Fingerprint Validation (`src/staccato/llm/validation.py`)

**Matching Strategy (in order):**
1. **Exact match** in original content
2. **Whitespace-normalized match** (line-by-line)
3. **Markup-removed exact match** (NEW)
4. **Markup-removed line-by-line match** (NEW)

```python
# If still not found, try with markup removal
if not found:
    cleaned_page_content = remove_markup_tags(page_content)
    cleaned_fingerprint = remove_markup_tags(fingerprint)
    
    # Try exact match on cleaned content
    if cleaned_fingerprint in cleaned_page_content:
        found = True
    else:
        # Try line-by-line with cleaned content
        cleaned_lines = cleaned_page_content.split('\n')
        for line in cleaned_lines:
            if cleaned_fingerprint in line.strip():
                found = True
                break
```

### 3. Enhanced Stitcher Matching (`src/staccato/core/stitcher.py`)

**Fallback Strategy:**
When fingerprint is not found with normal methods, the stitcher now:

1. Cleans both page content and fingerprint
2. Attempts to find the cleaned fingerprint in cleaned content
3. Maps the position back to original content (approximately)
4. Logs successful matches for monitoring

```python
# If still not found, try with markup removal
if not found:
    cleaned_page_content = remove_markup_tags(page_content)
    cleaned_fingerprint = remove_markup_tags(stripped_fingerprint)
    
    # Try exact match on cleaned content
    cleaned_index = cleaned_page_content.find(cleaned_fingerprint)
    if cleaned_index != -1:
        index = cleaned_index
        found = True
        logger.info(f"Found fingerprint after markup removal: '{event.fingerprint}' -> '{cleaned_fingerprint}'")
```

## Benefits

### 1. **Improved Matching Accuracy**
- Handles content with HTML tags, Markdown formatting
- Reduces false negatives in fingerprint validation
- More robust processing of diverse document formats

### 2. **Backward Compatibility**
- Markup removal is a fallback option
- Original matching logic preserved
- No performance impact for clean content

### 3. **Comprehensive Coverage**
- Supports multiple markup formats (HTML, Markdown)
- Handles nested and complex markup structures
- Graceful degradation for unknown markup

### 4. **Better Error Handling**
- Fewer fingerprint validation failures
- Reduced retry attempts due to markup issues
- More successful document processing

## Examples

### HTML Content
**Input:**
```html
<div class="section">
<h2>Safety procedures are complete.</h2>
</div>
```

**LLM Fingerprint:** `procedures are complete.`

**Matching Process:**
1. Direct match fails (markup in content)
2. Markup removal: `Safety procedures are complete.`
3. Match succeeds: `procedures are complete.` found in cleaned content

### Markdown Content
**Input:**
```markdown
This is the **introduction** to safety procedures.
```

**LLM Fingerprint:** `introduction to safety`

**Matching Process:**
1. Direct match fails (markdown formatting)
2. Markup removal: `This is the introduction to safety procedures.`
3. Match succeeds: `introduction to safety` found in cleaned content

### Mixed Markup
**Input:**
```html
<p>Check out [this link](http://example.com) for **more info**.</p>
```

**LLM Fingerprint:** `this link for more`

**Matching Process:**
1. Direct match fails (HTML + Markdown)
2. Markup removal: `Check out this link for more info.`
3. Match succeeds: `this link for more` found in cleaned content

## Testing

Comprehensive test coverage includes:

**✅ Markup Removal Function:**
- HTML tag removal
- Markdown formatting removal
- HTML entity conversion
- Whitespace cleanup
- Complex nested markup

**✅ Fingerprint Validation:**
- Validation with markup in page content
- Fallback to markup removal when needed
- Proper error handling

**✅ Stitcher Integration:**
- Event processing with markup content
- Gap detection with markup
- Logging of markup removal matches

**✅ Real-world Scenarios:**
- Mixed HTML and Markdown
- Nested markup structures
- Complex document formats

## Performance Considerations

**Efficiency:**
- Markup removal only triggered as fallback
- Regex patterns optimized for common cases
- Minimal overhead for clean content

**Memory Usage:**
- Temporary cleaned strings created only when needed
- No persistent storage of cleaned content
- Garbage collection friendly

## Integration

**Works seamlessly with existing features:**
- ✅ Page number validation and retry logic
- ✅ Fingerprint validation and retry logic
- ✅ Gap detection and automatic fixing
- ✅ Comprehensive error handling and logging

**Configuration:**
- No additional configuration required
- Automatic fallback behavior
- Transparent to users

## Future Enhancements

**Potential improvements:**
- Support for additional markup formats (LaTeX, reStructuredText)
- Configurable markup removal patterns
- Performance optimizations for large documents
- Caching of cleaned content for repeated use

## Summary

The markup removal implementation significantly improves fingerprint matching robustness by handling the disconnect between markup-rich source content and the clean rendered text that LLMs see. This reduces validation failures and improves overall document processing reliability while maintaining full backward compatibility.
