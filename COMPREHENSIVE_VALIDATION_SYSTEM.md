# Comprehensive Validation and Correction System

## Overview

The Staccato chunking engine now includes a comprehensive three-layer validation and correction system that ensures robust, error-resistant document processing. This system handles common LLM errors automatically and provides clear feedback for improvement.

## Three-Layer Validation System

### 🛡️ Layer 1: Page Number Validation
**Purpose:** Prevents crashes from invalid page references  
**Location:** `src/staccato/llm/validation.py` + `src/staccato/core/engine.py`

**What it does:**
- Validates that all page numbers in LLM responses are within the valid range for the current batch
- Automatically retries with corrective instructions when invalid page numbers are detected
- Prevents the KeyError crashes that occurred when LLM referenced non-existent pages

**Error handling:**
```python
# Example correction message sent to LLM
"IMPORTANT CORRECTION: Your previous response contained invalid page numbers: [99, 100]. 
You must ONLY use page numbers 1 through 3 (inclusive). 
Do not reference any page numbers outside this range."
```

### 🛡️ Layer 2: Fingerprint Validation  
**Purpose:** Ensures all fingerprints can be found in page content  
**Location:** `src/staccato/llm/validation.py` + `src/staccato/core/engine.py`

**What it does:**
- Validates that all fingerprints in STARTS/ENDS events exist in their respective page content
- Handles whitespace differences gracefully
- Automatically retries with helpful suggestions when fingerprints are not found

**Error handling:**
```python
# Example correction message sent to LLM
"IMPORTANT CORRECTION: Your previous response contained fingerprints that could not be found: 
['not found text', 'another missing']. Please check that you are copying the exact text 
from the document. Try using fewer words (2-3 words maximum) or check for typos in 
your fingerprints. The fingerprint must appear exactly as written in the page content."
```

### 🛡️ Layer 3: Gap Detection and Fixing
**Purpose:** Automatically recovers text missed by imperfect fingerprints  
**Location:** `src/staccato/core/stitcher.py`

**What it does:**
- Detects when there's text between an ENDS event and the next STARTS event
- Automatically adds the gap text to the ending chunk
- Ensures no content is lost due to non-contiguous fingerprints
- No LLM retry needed - handled transparently

**Example scenario:**
```
LLM says: ENDS at "section complete." then STARTS at "1. First step"
But there's text in between: "Additional notes here."
System automatically adds "Additional notes here." to the ending chunk.
```

## Implementation Details

### Page Number Validation

**Exception Class:**
```python
class PageNumberValidationError(Exception):
    def __init__(self, message: str, invalid_pages: List[int], valid_range: tuple[int, int]):
        self.invalid_pages = invalid_pages
        self.valid_range = valid_range
```

**Validation Function:**
```python
def validate_page_numbers(llm_response: LLMResponse, valid_page_numbers: List[int]) -> None:
    # Checks each event's page_number against valid_page_numbers
    # Raises PageNumberValidationError if any are invalid
```

### Fingerprint Validation

**Exception Class:**
```python
class FingerprintValidationError(Exception):
    def __init__(self, message: str, missing_fingerprints: List[dict], page_number: int):
        self.missing_fingerprints = missing_fingerprints
        self.page_number = page_number
```

**Validation Function:**
```python
def validate_fingerprints(llm_response: LLMResponse, page_content_map: dict[int, str]) -> None:
    # Checks each fingerprint can be found in its page content
    # Handles whitespace differences
    # Raises FingerprintValidationError if any are missing
```

### Gap Detection and Fixing

**Core Method:**
```python
def _check_and_fix_gap_after_ends(self, current_index: int, located_events: list, 
                                  page_content: str, ends_fingerprint_end: int, page_num: int):
    # Detects gaps between ENDS and next STARTS event
    # Automatically adds gap text to the ending chunk
    # Logs warnings for monitoring
```

## Retry Logic Flow

```
LLM Call
    ↓
Page Number Validation
    ↓ (if fails)
Add page range correction → Retry
    ↓ (if passes)
Fingerprint Validation  
    ↓ (if fails)
Add fingerprint suggestions → Retry
    ↓ (if passes)
Process Events with Gap Detection
    ↓
Success - No text lost!
```

## Configuration

**Retry Settings:**
```python
class RetryConfig(BaseSettings):
    page_validation_attempts: int = Field(
        default=3,
        description="Maximum retries for page/fingerprint validation"
    )
```

**Usage:**
```python
config = ChunkingEngineConfig(
    retry=RetryConfig(
        page_validation_attempts=5,  # More retries for difficult documents
    )
)
```

## Benefits

### 1. **Crash Prevention**
- No more KeyError crashes from invalid page numbers
- Graceful handling of fingerprint lookup failures
- Robust error recovery with automatic retries

### 2. **Content Preservation**
- Gap detection ensures no text is lost
- Automatic correction of non-contiguous fingerprints
- Complete content coverage even with LLM mistakes

### 3. **LLM Improvement**
- Clear, actionable error messages
- Specific suggestions for correction
- Helps LLM learn from mistakes

### 4. **Monitoring and Debugging**
- Comprehensive logging of all validation steps
- Clear warnings when gaps are detected
- Detailed error information for troubleshooting

## Testing

Comprehensive test suites verify:

**Page Validation:**
- ✅ Valid page numbers pass
- ✅ Invalid page numbers trigger retries
- ✅ Correction messages are properly formatted
- ✅ Max retries are respected

**Fingerprint Validation:**
- ✅ Valid fingerprints pass
- ✅ Missing fingerprints trigger retries
- ✅ Whitespace differences are handled
- ✅ CONTINUATION events are skipped

**Gap Detection:**
- ✅ Gaps are detected and fixed automatically
- ✅ Multiple gaps are handled correctly
- ✅ Whitespace-only gaps are ignored
- ✅ Contiguous fingerprints work normally

## Error Messages for LLM

The system provides clear, actionable feedback:

**Page Number Errors:**
- Specifies exactly which page numbers were invalid
- Provides the correct valid range
- Clear instruction to stay within bounds

**Fingerprint Errors:**
- Lists all missing fingerprints
- Suggests using fewer words
- Reminds to check for typos
- Emphasizes exact text matching

**Gap Detection:**
- Automatic - no LLM message needed
- Transparent recovery
- Logged for monitoring

## Backward Compatibility

- All existing APIs remain unchanged
- Configuration is optional (sensible defaults)
- No breaking changes to user code
- Enhanced robustness without complexity

## Performance Impact

- **Minimal overhead:** Validation is fast
- **Reduced retries:** Better error messages mean fewer failed attempts
- **No text loss:** Eliminates need for manual correction
- **Net positive:** Improved reliability outweighs small validation cost

This comprehensive system transforms the chunking engine from error-prone to robust, ensuring reliable document processing even with imperfect LLM responses.
