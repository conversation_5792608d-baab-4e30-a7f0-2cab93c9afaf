# Page ID Migration

## Summary

Successfully migrated the system from using `page_number` to `page_id` in the LLM response format. This change aligns with the updated prompts and ensures consistency throughout the codebase.

## Changes Made

### 1. Event Model Update (`src/staccato/llm/validation.py`)

**Before:**
```python
class Event(BaseModel):
    page_number: int = Field(
        ...,
        description="The 1-indexed page number where this event occurred."
    )
```

**After:**
```python
class Event(BaseModel):
    page_id: int = Field(
        ...,
        description="The page id where this event occurred."
    )
```

### 2. Validation Functions Update (`src/staccato/llm/validation.py`)

**Page Number Validation:**
- Updated `validate_page_numbers()` to use `event.page_id` instead of `event.page_number`
- Updated error handling to reference `page_id`

**Fingerprint Validation:**
- Updated `validate_fingerprints()` to use `event.page_id`
- Updated `FingerprintValidationError` to use `page_id` parameter
- Updated error grouping and reporting to use `page_id`

### 3. St<PERSON>er Updates (`src/staccato/core/stitcher.py`)

**Event Processing:**
```python
# Before
events_by_page[event.page_number].append(event)
start_page=event.page_number
end_page=event.page_number

# After  
events_by_page[event.page_id].append(event)
start_page=event.page_id
end_page=event.page_id
```

**Logging Updates:**
- Updated all log messages to use `page=event.page_id`

### 4. Response Schema Update (`src/staccato/llm/response_schema.py`)

**Schema Definition:**
```python
# Before
"page_number": {
    "type": "integer",
    "minimum": 1,
    "description": "The 1-indexed page number where this event occurred"
}
"required": ["event", "level", "page_number"]

# After
"page_id": {
    "type": "integer", 
    "minimum": 1,
    "description": "The page id where this event occurred"
}
"required": ["event", "level", "page_id"]
```

**Example Response:**
```python
# Before
{
    "event": "STARTS",
    "level": "section", 
    "page_number": 5,
    "fingerprint": "text"
}

# After
{
    "event": "STARTS",
    "level": "section",
    "page_id": 5, 
    "fingerprint": "text"
}
```

## Validation and Testing

### Comprehensive Testing
All functionality has been tested and verified:

**✅ Event Model:**
- Events can be created with `page_id` field
- LLMResponse accepts events with `page_id`
- Serialization/deserialization works correctly

**✅ Page Validation:**
- `validate_page_numbers()` works with `page_id`
- Invalid `page_id` values are caught correctly
- Error messages reference correct page IDs

**✅ Fingerprint Validation:**
- `validate_fingerprints()` works with `page_id`
- Missing fingerprints are reported with correct page IDs
- Error handling preserves `page_id` information

**✅ Stitcher Processing:**
- Events are grouped by `page_id` correctly
- Chunks are created with correct start/end page IDs
- Gap detection works with `page_id`
- Logging shows correct page IDs

## Backward Compatibility

**Breaking Change Notice:**
This is a breaking change for the LLM response format. The LLM must now return `page_id` instead of `page_number` in event objects.

**What Changed:**
- LLM responses must use `"page_id": X` instead of `"page_number": X`
- All internal processing now uses `page_id`
- JSON schema validation expects `page_id`

**What Stayed the Same:**
- All validation and correction logic works identically
- Page formatting in prompts unchanged (still uses "THIS IS PAGE NUMBER X")
- Internal page content mapping still uses integer keys
- All retry and error handling logic preserved

## Integration Points

### Prompt Alignment
The change aligns with the updated prompts that specify:
```
"page_id": (Required) The page id (as provided on top of the page) where the event occurs.
```

### Engine Compatibility
The engine continues to work seamlessly:
- Page content mapping uses the same integer keys
- Validation functions accept the same parameters
- Error handling and retry logic unchanged

### Error Messages
Error messages now correctly reference page IDs:
```
"Your previous response contained invalid page numbers: [99]. 
You must ONLY use page numbers 1 through 3 (inclusive)."
```

## Benefits

1. **Consistency:** Aligns code with updated prompt terminology
2. **Clarity:** `page_id` is more generic than `page_number`
3. **Future-Proof:** Allows for non-sequential page identifiers if needed
4. **Maintained Functionality:** All validation and correction features work identically

## Migration Complete

The system has been successfully migrated to use `page_id` throughout:

- ✅ Event model updated
- ✅ Validation functions updated  
- ✅ Stitcher logic updated
- ✅ Response schema updated
- ✅ Error handling updated
- ✅ Logging updated
- ✅ All tests passing

The LLM can now use the new `page_id` format as specified in the updated prompts, and all existing functionality (page validation, fingerprint validation, gap detection, retry logic) continues to work seamlessly.
