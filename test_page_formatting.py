#!/usr/bin/env python3
"""
Test script to verify the new page formatting with clear headers.
"""

from unittest.mock import MagicMock
from staccato.core.engine import ChunkingEngine
from staccato.config import Chunking<PERSON>ngineConfig, LLMConfig, PreprocessingConfig


def test_page_formatting():
    """Test that pages are formatted with clear headers."""
    print("🧪 Testing page formatting with clear headers...")
    
    # Create a mock engine to test the page formatting logic
    config = ChunkingEngineConfig(
        llm=LLMConfig(provider="openai", model_name="gpt-4"),
        preprocessing=PreprocessingConfig(page_batch_size=3)
    )
    
    # We'll test the page formatting logic by examining what would be passed to the LLM
    # Create mock page content
    mock_pages = [
        MagicMock(text="This is the content of the first page. It contains important information about the topic."),
        MagicMock(text="Second page content here. This page continues the discussion with more details."),
        MagicMock(text="Third page has the final content. This concludes the document section.")
    ]
    
    # Simulate the page content mapping logic from the engine
    page_numbers = [1, 2, 3]
    page_content_map = {
        page_num: page.text for page_num, page in zip(page_numbers, mock_pages)
    }
    
    # Simulate the page content combination logic
    page_contents = []
    for page_num, content in page_content_map.items():
        page_header = f"THIS IS PAGE NUMBER {page_num}"
        separator_line = "=" * len(page_header)
        page_contents.append(f"{separator_line}\n{page_header}\n{separator_line}\n\n{content}")
    
    combined_page_content = "\n\n\n".join(page_contents)
    
    print("📄 Generated combined page content:")
    print("=" * 80)
    print(combined_page_content)
    print("=" * 80)
    
    # Verify the formatting
    lines = combined_page_content.split('\n')
    
    # Check that each page has the correct header format
    page_headers_found = []
    for i, line in enumerate(lines):
        if line.startswith("THIS IS PAGE NUMBER"):
            page_headers_found.append(line)
            
            # Check that there's a separator line before and after
            if i > 0:
                separator_before = lines[i-1]
                assert separator_before.startswith("="), f"Expected separator before header, got: '{separator_before}'"
            
            if i < len(lines) - 1:
                separator_after = lines[i+1]
                assert separator_after.startswith("="), f"Expected separator after header, got: '{separator_after}'"
    
    print(f"\n📊 Found {len(page_headers_found)} page headers:")
    for header in page_headers_found:
        print(f"   ✅ {header}")
    
    # Verify we have the expected headers
    expected_headers = ["THIS IS PAGE NUMBER 1", "THIS IS PAGE NUMBER 2", "THIS IS PAGE NUMBER 3"]
    assert page_headers_found == expected_headers, f"Expected {expected_headers}, got {page_headers_found}"
    
    # Verify clear separation between pages
    page_separations = combined_page_content.split("\n\n\n")
    assert len(page_separations) == 3, f"Expected 3 page sections, got {len(page_separations)}"
    
    print("\n✅ Page formatting verification passed!")
    
    # Check that each page section contains the expected content
    for i, section in enumerate(page_separations):
        page_num = i + 1
        expected_content = mock_pages[i].text
        assert expected_content in section, f"Page {page_num} content not found in section"
        assert f"THIS IS PAGE NUMBER {page_num}" in section, f"Page {page_num} header not found"
    
    print("✅ Page content verification passed!")
    
    return True


def test_single_page_formatting():
    """Test formatting with a single page."""
    print("\n🧪 Testing single page formatting...")
    
    page_content_map = {1: "Single page content here."}
    
    page_contents = []
    for page_num, content in page_content_map.items():
        page_header = f"THIS IS PAGE NUMBER {page_num}"
        separator_line = "=" * len(page_header)
        page_contents.append(f"{separator_line}\n{page_header}\n{separator_line}\n\n{content}")
    
    combined_page_content = "\n\n\n".join(page_contents)
    
    print("📄 Single page content:")
    print(combined_page_content)
    
    # Verify single page formatting
    assert "THIS IS PAGE NUMBER 1" in combined_page_content
    assert "Single page content here." in combined_page_content
    assert combined_page_content.count("=") >= 2  # At least two separator lines
    
    print("✅ Single page formatting works correctly!")
    return True


def test_large_page_numbers():
    """Test formatting with larger page numbers."""
    print("\n🧪 Testing formatting with larger page numbers...")
    
    page_content_map = {
        15: "Content from page fifteen.",
        16: "Content from page sixteen.", 
        17: "Content from page seventeen."
    }
    
    page_contents = []
    for page_num, content in page_content_map.items():
        page_header = f"THIS IS PAGE NUMBER {page_num}"
        separator_line = "=" * len(page_header)
        page_contents.append(f"{separator_line}\n{page_header}\n{separator_line}\n\n{content}")
    
    combined_page_content = "\n\n\n".join(page_contents)
    
    print("📄 Large page numbers content:")
    print(combined_page_content)
    
    # Verify large page number formatting
    assert "THIS IS PAGE NUMBER 15" in combined_page_content
    assert "THIS IS PAGE NUMBER 16" in combined_page_content
    assert "THIS IS PAGE NUMBER 17" in combined_page_content
    
    # Check that separator lines are properly sized
    lines = combined_page_content.split('\n')
    for i, line in enumerate(lines):
        if line.startswith("THIS IS PAGE NUMBER"):
            # Check separator lines have correct length
            if i > 0:
                separator_before = lines[i-1]
                assert len(separator_before) == len(line), f"Separator length mismatch for '{line}'"
            if i < len(lines) - 1:
                separator_after = lines[i+1]
                assert len(separator_after) == len(line), f"Separator length mismatch for '{line}'"
    
    print("✅ Large page numbers formatting works correctly!")
    return True


def main():
    """Run all formatting tests."""
    print("🚀 Testing page formatting with clear headers")
    print("=" * 60)
    
    success = True
    
    try:
        if not test_page_formatting():
            success = False
    except Exception as e:
        print(f"❌ test_page_formatting failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_single_page_formatting():
            success = False
    except Exception as e:
        print(f"❌ test_single_page_formatting failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_large_page_numbers():
            success = False
    except Exception as e:
        print(f"❌ test_large_page_numbers failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    if success:
        print("\n🎉 All page formatting tests passed!")
        print("\n📋 Summary of new page formatting:")
        print("   ✅ Clear 'THIS IS PAGE NUMBER X' headers")
        print("   ✅ Separator lines (=) above and below headers")
        print("   ✅ Triple newline separation between pages")
        print("   ✅ Consistent formatting for all page numbers")
        print("\n🔧 The LLM will now receive much clearer page boundaries!")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
