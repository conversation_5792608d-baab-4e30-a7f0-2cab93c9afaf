#!/usr/bin/env python3
"""
Demonstration of the retry logic for page number validation.
This simulates what happens when the LLM returns invalid page numbers.
"""

from staccato.llm.validation import Event, LLMResponse, validate_page_numbers, PageNumberValidationError


def simulate_llm_call_with_retry(page_numbers, max_retries=3):
    """
    Simulates the retry logic that's now implemented in the engine.
    This demonstrates how the system handles invalid page numbers.
    """
    print(f"🔄 Simulating LLM processing for pages: {page_numbers}")
    print(f"📋 Max retries allowed: {max_retries}")
    
    # Simulate different LLM responses for each retry attempt
    simulated_responses = [
        # First attempt - LLM returns invalid page numbers
        LLMResponse(events=[
            Event(event="STARTS", level="section", page_number=99, title="Invalid Section", fingerprint="text1"),
            Event(event="ENDS", level="section", page_number=100, fingerprint="text2"),
        ]),
        # Second attempt - LLM still returns some invalid page numbers
        LLMResponse(events=[
            Event(event="STARTS", level="section", page_number=1, title="Valid Section", fingerprint="text1"),
            Event(event="ENDS", level="section", page_number=50, fingerprint="text2"),  # Still invalid
        ]),
        # Third attempt - LLM finally returns valid page numbers
        LLMResponse(events=[
            Event(event="STARTS", level="section", page_number=1, title="Valid Section", fingerprint="text1"),
            Event(event="ENDS", level="section", page_number=2, fingerprint="text2"),
            Event(event="CONTINUATION", level="section", page_number=3),
        ]),
    ]
    
    user_prompt = "Original prompt: Please analyze the document and identify structural elements..."
    
    for retry_attempt in range(max_retries):
        print(f"\n📞 LLM Call Attempt {retry_attempt + 1}")
        print(f"   Prompt length: {len(user_prompt)} characters")
        
        if retry_attempt > 0:
            print("   🔧 This is a retry with corrective instructions")
        
        # Simulate LLM response
        if retry_attempt < len(simulated_responses):
            llm_response = simulated_responses[retry_attempt]
        else:
            # Fallback to valid response
            llm_response = simulated_responses[-1]
        
        print(f"   📤 LLM returned {len(llm_response.events)} events")
        for event in llm_response.events:
            print(f"      - {event.event} {event.level} on page {event.page_number}")
        
        try:
            # Validate page numbers
            validate_page_numbers(llm_response, page_numbers)
            print("   ✅ Page validation successful!")
            return llm_response, retry_attempt + 1
            
        except PageNumberValidationError as e:
            print(f"   ❌ Page validation failed: {e}")
            
            if retry_attempt == max_retries - 1:
                print(f"   🚫 Max retries ({max_retries}) exceeded. Giving up.")
                raise
            
            # Add corrective instructions to the user prompt for retry
            min_page, max_page = e.valid_range
            correction_text = (
                f"\n\nIMPORTANT CORRECTION: Your previous response contained invalid page numbers: {e.invalid_pages}. "
                f"You must ONLY use page numbers {min_page} through {max_page} (inclusive). "
                f"Do not reference any page numbers outside this range."
            )
            user_prompt = user_prompt + correction_text
            print(f"   🔧 Added correction instructions for retry")
            print(f"   📝 Correction: Use only pages {min_page}-{max_page}")


def demo_successful_retry():
    """Demonstrate a successful retry scenario."""
    print("🎬 DEMO 1: Successful Retry Scenario")
    print("=" * 50)
    
    try:
        response, attempts = simulate_llm_call_with_retry([1, 2, 3], max_retries=3)
        print(f"\n🎉 SUCCESS! Got valid response after {attempts} attempts")
        print(f"📊 Final response contains {len(response.events)} valid events")
        
    except Exception as e:
        print(f"\n💥 FAILED: {e}")


def demo_max_retries_exceeded():
    """Demonstrate what happens when max retries are exceeded."""
    print("\n\n🎬 DEMO 2: Max Retries Exceeded Scenario")
    print("=" * 50)
    
    # Simulate a scenario where LLM keeps returning invalid page numbers
    def simulate_persistent_failure():
        page_numbers = [1, 2, 3]
        max_retries = 2  # Lower retry count for demo
        
        print(f"🔄 Simulating persistent LLM failure for pages: {page_numbers}")
        print(f"📋 Max retries allowed: {max_retries}")
        
        # Always return invalid responses
        invalid_response = LLMResponse(events=[
            Event(event="STARTS", level="section", page_number=99, title="Persistent Invalid", fingerprint="text1"),
        ])
        
        for retry_attempt in range(max_retries):
            print(f"\n📞 LLM Call Attempt {retry_attempt + 1}")
            print(f"   📤 LLM returned invalid page numbers: [99]")
            
            try:
                validate_page_numbers(invalid_response, page_numbers)
                print("   ✅ Page validation successful!")
                return invalid_response, retry_attempt + 1
                
            except PageNumberValidationError as e:
                print(f"   ❌ Page validation failed: {e}")
                
                if retry_attempt == max_retries - 1:
                    print(f"   🚫 Max retries ({max_retries}) exceeded. Giving up.")
                    raise
                
                print(f"   🔧 Will retry with correction instructions")
    
    try:
        simulate_persistent_failure()
        print(f"\n💥 UNEXPECTED: Should have failed!")
        
    except PageNumberValidationError as e:
        print(f"\n🛑 EXPECTED FAILURE: {e}")
        print("📋 This is the correct behavior when LLM persistently returns invalid page numbers")


def demo_configuration():
    """Show how the retry behavior can be configured."""
    print("\n\n🎬 DEMO 3: Configuration Options")
    print("=" * 50)
    
    print("🔧 The retry behavior can be configured in ChunkingEngineConfig:")
    print()
    print("```python")
    print("from staccato import ChunkingEngineConfig, RetryConfig")
    print()
    print("config = ChunkingEngineConfig(")
    print("    retry=RetryConfig(")
    print("        page_validation_attempts=5,  # Allow more retries")
    print("        attempts=3,                  # LLM API retries")
    print("        min_wait=1,                  # Retry timing")
    print("        max_wait=10")
    print("    )")
    print(")")
    print("```")
    print()
    print("📋 Default configuration:")
    print("   - page_validation_attempts: 3")
    print("   - Each retry includes corrective instructions about valid page range")
    print("   - Original LLM retry logic (for API failures) remains unchanged")


def main():
    """Run all demonstrations."""
    print("🚀 Page Number Validation Retry Logic Demonstration")
    print("=" * 60)
    print()
    print("This demonstrates the new retry logic that handles cases where")
    print("the LLM returns page numbers outside the valid range.")
    print()
    
    demo_successful_retry()
    demo_max_retries_exceeded()
    demo_configuration()
    
    print("\n\n🎯 SUMMARY")
    print("=" * 50)
    print("✅ The engine now automatically retries when LLM returns invalid page numbers")
    print("✅ Each retry includes specific instructions about the valid page range")
    print("✅ Configurable maximum retry attempts (default: 3)")
    print("✅ Clear error messages when max retries are exceeded")
    print("✅ Original LLM API retry logic remains unchanged")
    print()
    print("🔧 This should resolve the KeyError issues you were experiencing!")


if __name__ == "__main__":
    main()
