#!/usr/bin/env python3
"""
Test script to verify the new ENDS event logic works correctly.
The ENDS fingerprint now contains the last few words of the chunk being ended.
"""

from staccato.core.stitcher import StatefulStitcher
from staccato.llm.validation import Event, LLMResponse


def test_new_ends_logic():
    """Test that ENDS events correctly include the fingerprint in the ending chunk."""
    print("🧪 Testing new ENDS event logic...")
    
    # Create a stitcher
    stitcher = StatefulStitcher()
    
    # Simulate page content
    page_content = """This is the introduction to safety procedures. All employees must follow these guidelines carefully.

Safety procedures are complete.

1. Turn off all equipment immediately
2. Evacuate the building
3. Call emergency services"""
    
    # Create events with the new ENDS logic
    events = [
        Event(
            event="STARTS", 
            level="section", 
            page_number=1, 
            title="Safety Procedures", 
            fingerprint="This is the introduction"
        ),
        Event(
            event="ENDS", 
            level="section", 
            page_number=1, 
            fingerprint="procedures are complete."  # Last words of the section
        ),
        Event(
            event="STARTS", 
            level="list", 
            page_number=1, 
            title="Emergency Steps", 
            fingerprint="1. Turn off"  # First words of the list
        )
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Process the events
    stitcher.process_events(llm_response, page_content_map)
    
    # Finalize to get completed chunks
    completed_chunks = stitcher.finalize()
    
    print(f"📊 Generated {len(completed_chunks)} chunks")
    
    # Verify the results
    assert len(completed_chunks) == 2, f"Expected 2 chunks, got {len(completed_chunks)}"
    
    # Check first chunk (section)
    section_chunk = completed_chunks[0]
    print(f"\n📝 Section chunk:")
    print(f"   Title: {section_chunk.title}")
    print(f"   Level: {section_chunk.level}")
    print(f"   Text: '{section_chunk.text_content}'")
    
    # The section chunk should include everything up to and including "procedures are complete."
    expected_section_text = "This is the introduction to safety procedures. All employees must follow these guidelines carefully.\n\nSafety procedures are complete."
    
    assert section_chunk.title == "Safety Procedures"
    assert section_chunk.level == "section"
    assert section_chunk.text_content.strip() == expected_section_text.strip(), \
        f"Section text mismatch.\nExpected: '{expected_section_text}'\nActual: '{section_chunk.text_content}'"
    
    # Check second chunk (list)
    list_chunk = completed_chunks[1]
    print(f"\n📝 List chunk:")
    print(f"   Title: {list_chunk.title}")
    print(f"   Level: {list_chunk.level}")
    print(f"   Text: '{list_chunk.text_content}'")
    
    # The list chunk should start with "1. Turn off" and include the rest
    expected_list_text = "1. Turn off all equipment immediately\n2. Evacuate the building\n3. Call emergency services"
    
    assert list_chunk.title == "Emergency Steps"
    assert list_chunk.level == "list"
    assert list_chunk.text_content.strip() == expected_list_text.strip(), \
        f"List text mismatch.\nExpected: '{expected_list_text}'\nActual: '{list_chunk.text_content}'"
    
    print("\n✅ All assertions passed!")
    return True


def test_ends_without_starts():
    """Test ENDS event when there's already an active chunk from previous pages."""
    print("\n🧪 Testing ENDS event with pre-existing active chunk...")
    
    stitcher = StatefulStitcher()
    
    # Simulate that we already have an active chunk from previous processing
    from staccato.internal.models import ActiveChunk
    existing_chunk = ActiveChunk(
        level="section",
        title="Previous Section",
        text_content="Content from previous pages. ",
        start_page=1
    )
    stitcher.active_stack.append(existing_chunk)
    
    # Page content for current page
    page_content = "This continues the section. The section ends here."
    
    # ENDS event with fingerprint containing last words
    events = [
        Event(
            event="ENDS",
            level="section", 
            page_number=2,
            fingerprint="section ends here."
        )
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {2: page_content}
    
    # Process the events
    stitcher.process_events(llm_response, page_content_map)
    
    # Check that the chunk was completed correctly
    completed_chunks = stitcher.completed_chunks
    assert len(completed_chunks) == 1, f"Expected 1 completed chunk, got {len(completed_chunks)}"
    
    completed_chunk = completed_chunks[0]
    print(f"\n📝 Completed chunk:")
    print(f"   Title: {completed_chunk.title}")
    print(f"   Text: '{completed_chunk.text_content}'")
    
    # Should include content from previous pages plus current page up to and including the fingerprint
    expected_text = "Content from previous pages. This continues the section. The section ends here."
    assert completed_chunk.text_content == expected_text, \
        f"Text mismatch.\nExpected: '{expected_text}'\nActual: '{completed_chunk.text_content}'"
    
    print("✅ ENDS with pre-existing chunk works correctly!")
    return True


def test_multiple_ends_starts():
    """Test multiple ENDS and STARTS events in sequence."""
    print("\n🧪 Testing multiple ENDS and STARTS events...")
    
    stitcher = StatefulStitcher()
    
    page_content = """First section content goes here. This is the end of first section.

Second section starts here. More content for second section. Second section concludes.

Third section begins now. Final content here."""
    
    events = [
        Event(event="STARTS", level="section", page_number=1, title="First Section", fingerprint="First section content"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="end of first section."),
        Event(event="STARTS", level="section", page_number=1, title="Second Section", fingerprint="Second section starts"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="section concludes."),
        Event(event="STARTS", level="section", page_number=1, title="Third Section", fingerprint="Third section begins"),
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    stitcher.process_events(llm_response, page_content_map)
    completed_chunks = stitcher.finalize()
    
    print(f"📊 Generated {len(completed_chunks)} chunks")
    
    # Should have 3 chunks total (2 completed + 1 finalized)
    assert len(completed_chunks) == 3, f"Expected 3 chunks, got {len(completed_chunks)}"
    
    # Check each chunk
    chunk1 = completed_chunks[0]
    chunk2 = completed_chunks[1] 
    chunk3 = completed_chunks[2]
    
    print(f"\n📝 Chunk 1: '{chunk1.text_content}'")
    print(f"📝 Chunk 2: '{chunk2.text_content}'")
    print(f"📝 Chunk 3: '{chunk3.text_content}'")
    
    assert "First section content goes here. This is the end of first section." in chunk1.text_content
    assert "Second section starts here. More content for second section. Second section concludes." in chunk2.text_content
    assert "Third section begins now. Final content here." in chunk3.text_content
    
    print("✅ Multiple ENDS/STARTS sequence works correctly!")
    return True


def main():
    """Run all tests."""
    print("🚀 Testing new ENDS event logic (fingerprint = last words of chunk)")
    print("=" * 70)
    
    success = True
    
    try:
        if not test_new_ends_logic():
            success = False
    except Exception as e:
        print(f"❌ test_new_ends_logic failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_ends_without_starts():
            success = False
    except Exception as e:
        print(f"❌ test_ends_without_starts failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        if not test_multiple_ends_starts():
            success = False
    except Exception as e:
        print(f"❌ test_multiple_ends_starts failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    if success:
        print("\n🎉 All tests passed! The new ENDS logic is working correctly.")
        print("\n📋 Summary of changes:")
        print("   ✅ ENDS fingerprint now contains last words of the chunk being ended")
        print("   ✅ Text slicing includes the fingerprint in the ending chunk")
        print("   ✅ Proper boundary handling between chunks")
        print("   ✅ Works with pre-existing active chunks")
        print("   ✅ Handles multiple ENDS/STARTS sequences")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
