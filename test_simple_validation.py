#!/usr/bin/env python3
"""
Simple test to verify the page validation logic works correctly.
"""

from staccato.llm.validation import Event, LLMResponse, validate_page_numbers, PageNumberValidationError


def test_page_validation_scenarios():
    """Test various page validation scenarios."""
    print("🧪 Testing page validation scenarios...")
    
    # Test 1: Valid page numbers
    print("\n1. Testing valid page numbers...")
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_number=2, fingerprint="text2"),
        Event(event="CONTINUATION", level="section", page_number=3),
    ]
    response = LLMResponse(events=events)
    valid_pages = [1, 2, 3]
    
    try:
        validate_page_numbers(response, valid_pages)
        print("   ✅ Valid page numbers passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False
    
    # Test 2: Invalid page numbers
    print("\n2. Testing invalid page numbers...")
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_number=5, fingerprint="text2"),  # Invalid
        Event(event="CONTINUATION", level="section", page_number=10),  # Invalid
    ]
    response = LLMResponse(events=events)
    valid_pages = [1, 2, 3]
    
    try:
        validate_page_numbers(response, valid_pages)
        print("   ❌ Should have raised PageNumberValidationError")
        return False
    except PageNumberValidationError as e:
        print(f"   ✅ Correctly caught invalid page numbers: {e.invalid_pages}")
        print(f"   ✅ Valid range: {e.valid_range}")
        print(f"   ✅ Error message: {str(e)}")
        
        # Verify the error details
        if e.invalid_pages != [5, 10]:
            print(f"   ❌ Expected invalid pages [5, 10], got {e.invalid_pages}")
            return False
        if e.valid_range != (1, 3):
            print(f"   ❌ Expected valid range (1, 3), got {e.valid_range}")
            return False
    except Exception as e:
        print(f"   ❌ Unexpected error type: {e}")
        return False
    
    # Test 3: Edge case - page numbers outside batch range
    print("\n3. Testing page numbers outside batch range...")
    events = [
        Event(event="STARTS", level="section", page_number=0, title="Section 1", fingerprint="text1"),  # Too low
        Event(event="ENDS", level="section", page_number=100, fingerprint="text2"),  # Too high
    ]
    response = LLMResponse(events=events)
    valid_pages = [5, 6, 7]  # Batch processing pages 5-7
    
    try:
        validate_page_numbers(response, valid_pages)
        print("   ❌ Should have raised PageNumberValidationError")
        return False
    except PageNumberValidationError as e:
        print(f"   ✅ Correctly caught out-of-range pages: {e.invalid_pages}")
        print(f"   ✅ Valid range: {e.valid_range}")
        
        if e.invalid_pages != [0, 100]:
            print(f"   ❌ Expected invalid pages [0, 100], got {e.invalid_pages}")
            return False
        if e.valid_range != (5, 7):
            print(f"   ❌ Expected valid range (5, 7), got {e.valid_range}")
            return False
    
    # Test 4: Empty events (should pass)
    print("\n4. Testing empty events...")
    response = LLMResponse(events=[])
    valid_pages = [1, 2, 3]
    
    try:
        validate_page_numbers(response, valid_pages)
        print("   ✅ Empty events passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False
    
    return True


def test_error_message_format():
    """Test that error messages contain the expected correction instructions."""
    print("\n🧪 Testing error message format for retry instructions...")
    
    events = [
        Event(event="STARTS", level="section", page_number=15, title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_number=20, fingerprint="text2"),
    ]
    response = LLMResponse(events=events)
    valid_pages = [1, 2, 3]
    
    try:
        validate_page_numbers(response, valid_pages)
        print("   ❌ Should have raised PageNumberValidationError")
        return False
    except PageNumberValidationError as e:
        error_msg = str(e)
        print(f"   📝 Error message: {error_msg}")
        
        # Check that the error message contains useful information for retry
        if "invalid page numbers: [15, 20]" not in error_msg:
            print("   ❌ Error message missing invalid page numbers")
            return False
        
        if "Valid page range is 1-3" not in error_msg:
            print("   ❌ Error message missing valid page range")
            return False
        
        print("   ✅ Error message format is correct for retry instructions")
        
        # Simulate how the engine would use this for retry
        min_page, max_page = e.valid_range
        correction_text = (
            f"\n\nIMPORTANT CORRECTION: Your previous response contained invalid page numbers: {e.invalid_pages}. "
            f"You must ONLY use page numbers {min_page} through {max_page} (inclusive). "
            f"Do not reference any page numbers outside this range."
        )
        
        print(f"   📝 Generated correction text: {correction_text}")
        print("   ✅ Correction text generation works correctly")
        
        return True


def main():
    """Run all tests."""
    print("🚀 Starting page validation tests...\n")
    
    success = True
    
    if not test_page_validation_scenarios():
        success = False
    
    if not test_error_message_format():
        success = False
    
    if success:
        print("\n🎉 All tests passed! The page validation logic is working correctly.")
        print("\n📋 Summary of what was tested:")
        print("   ✅ Valid page numbers pass validation")
        print("   ✅ Invalid page numbers are correctly identified")
        print("   ✅ Out-of-range page numbers are caught")
        print("   ✅ Empty events are handled gracefully")
        print("   ✅ Error messages contain useful retry information")
        print("\n🔧 The engine retry logic should now work correctly when LLM returns invalid page numbers!")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
