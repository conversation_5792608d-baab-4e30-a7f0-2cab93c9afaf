#!/usr/bin/env python3
"""
Test script to verify alphanumeric page ID functionality.
"""

from staccato.core.engine import ChunkingEngine
from staccato.config import ChunkingEngineConfig, LLMConfig
from staccato.llm.validation import Event, LLMResponse


def test_page_id_generation():
    """Test that page ID generation is consistent and alphanumeric."""
    print("🧪 Testing page ID generation...")
    
    config = ChunkingEngineConfig(llm=LLMConfig(provider="openai", model_name="gpt-4"))
    engine = ChunkingEngine(config)
    
    # Test consistent generation
    page_ids = {}
    for page_num in [1, 2, 3, 15, 100]:
        page_id = engine._generate_page_id(page_num)
        page_ids[page_num] = page_id
        print(f"   Page {page_num} -> ID '{page_id}'")
        
        # Verify it's 4 characters
        assert len(page_id) == 4, f"Page ID should be 4 characters, got {len(page_id)}"
        
        # Verify it's alphanumeric
        assert page_id.isalnum(), f"Page ID should be alphanumeric, got '{page_id}'"
        
        # Verify consistency (same page number should always generate same ID)
        page_id2 = engine._generate_page_id(page_num)
        assert page_id == page_id2, f"Page ID generation should be consistent"
    
    # Verify different page numbers generate different IDs
    unique_ids = set(page_ids.values())
    assert len(unique_ids) == len(page_ids), f"All page IDs should be unique"
    
    print("   ✅ Page ID generation works correctly")
    return True


def test_page_id_validation():
    """Test page ID validation logic."""
    print("\n🧪 Testing page ID validation...")
    
    config = ChunkingEngineConfig(llm=LLMConfig(provider="openai", model_name="gpt-4"))
    engine = ChunkingEngine(config)
    
    # Create page ID mapping
    page_id_reverse_map = {
        "A1B2": 1,
        "C3D4": 2,
        "E5F6": 3
    }
    
    # Test valid page IDs
    events = [
        Event(event="STARTS", level="section", page_id="A1B2", title="Section", fingerprint="text"),
        Event(event="ENDS", level="section", page_id="C3D4", fingerprint="text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        engine._validate_page_ids(response, page_id_reverse_map)
        print("   ✅ Valid page IDs passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False
    
    # Test invalid page IDs
    events = [
        Event(event="STARTS", level="section", page_id="X9Y8", title="Section", fingerprint="text"),  # Invalid
    ]
    response = LLMResponse(events=events)
    
    try:
        engine._validate_page_ids(response, page_id_reverse_map)
        print("   ❌ Should have failed!")
        return False
    except Exception as e:
        print(f"   ✅ Correctly caught invalid page ID: {e}")
        assert "X9Y8" in str(e)
    
    return True


def test_page_id_conversion():
    """Test conversion between page IDs and page numbers."""
    print("\n🧪 Testing page ID conversion...")
    
    config = ChunkingEngineConfig(llm=LLMConfig(provider="openai", model_name="gpt-4"))
    engine = ChunkingEngine(config)
    
    # Create page ID mapping
    page_id_reverse_map = {
        "A1B2": 1,
        "C3D4": 2,
        "E5F6": 3
    }
    
    # Create LLM response with page IDs
    events = [
        Event(event="STARTS", level="section", page_id="A1B2", title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_id="C3D4", fingerprint="text2"),
        Event(event="STARTS", level="section", page_id="E5F6", title="Section 2", fingerprint="text3"),
    ]
    response = LLMResponse(events=events)
    
    # Convert page IDs to page numbers
    converted_response = engine._convert_page_ids_to_numbers(response, page_id_reverse_map)
    
    print("   📝 Original events (with page IDs):")
    for event in response.events:
        print(f"      {event.event} on page_id '{event.page_id}'")
    
    print("   📝 Converted events (with page numbers):")
    for event in converted_response.events:
        print(f"      {event.event} on page_id {event.page_id}")
    
    # Verify conversion
    expected_page_numbers = [1, 2, 3]
    actual_page_numbers = [event.page_id for event in converted_response.events]
    
    assert actual_page_numbers == expected_page_numbers, f"Expected {expected_page_numbers}, got {actual_page_numbers}"
    
    print("   ✅ Page ID to page number conversion works correctly")
    return True


def test_page_header_formatting():
    """Test that page headers use alphanumeric IDs."""
    print("\n🧪 Testing page header formatting with alphanumeric IDs...")
    
    config = ChunkingEngineConfig(llm=LLMConfig(provider="openai", model_name="gpt-4"))
    engine = ChunkingEngine(config)
    
    # Simulate page processing
    page_numbers = [1, 2, 3]
    page_content_map = {
        1: "Content of page 1",
        2: "Content of page 2", 
        3: "Content of page 3"
    }
    
    # Generate page ID mapping
    page_id_map = {}
    for page_num in page_numbers:
        page_id = engine._generate_page_id(page_num)
        page_id_map[page_num] = page_id
    
    print(f"   📋 Page ID mapping: {page_id_map}")
    
    # Create page content with headers
    page_contents = []
    for page_num, content in page_content_map.items():
        page_id = page_id_map[page_num]
        page_header = f"THIS IS PAGE ID {page_id}"
        separator_line = "=" * len(page_header)
        page_contents.append(f"{separator_line}\n{page_header}\n{separator_line}\n\n{content}")
    
    combined_content = "\n\n\n".join(page_contents)
    
    print("   📄 Generated page content:")
    print(combined_content)
    
    # Verify that page headers contain alphanumeric IDs, not numbers
    for page_num, page_id in page_id_map.items():
        expected_header = f"THIS IS PAGE ID {page_id}"
        assert expected_header in combined_content, f"Expected header '{expected_header}' not found"
        
        # Verify old numeric format is NOT present
        old_header = f"THIS IS PAGE ID {page_num}"
        if page_id != str(page_num):  # Only check if they're different
            assert old_header not in combined_content, f"Old numeric header '{old_header}' should not be present"
    
    print("   ✅ Page headers use alphanumeric IDs correctly")
    return True


def test_error_message_format():
    """Test that error messages show alphanumeric page IDs."""
    print("\n🧪 Testing error message format with alphanumeric IDs...")
    
    config = ChunkingEngineConfig(llm=LLMConfig(provider="openai", model_name="gpt-4"))
    engine = ChunkingEngine(config)
    
    page_id_reverse_map = {
        "A1B2": 1,
        "C3D4": 2,
        "E5F6": 3
    }
    
    # Test with invalid page ID
    events = [
        Event(event="STARTS", level="section", page_id="INVALID", title="Section", fingerprint="text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        engine._validate_page_ids(response, page_id_reverse_map)
        print("   ❌ Should have failed!")
        return False
    except Exception as e:
        error_msg = str(e)
        print(f"   📝 Error message: {error_msg}")
        
        # Verify error message contains alphanumeric IDs
        assert "A1B2" in error_msg or "C3D4" in error_msg or "E5F6" in error_msg, "Error should show valid alphanumeric IDs"
        assert "INVALID" in error_msg, "Error should show the invalid ID"
        
        print("   ✅ Error message format is correct")
        return True


def main():
    """Run all alphanumeric page ID tests."""
    print("🚀 Testing alphanumeric page ID system")
    print("=" * 60)
    
    success = True
    
    if not test_page_id_generation():
        success = False
    
    if not test_page_id_validation():
        success = False
    
    if not test_page_id_conversion():
        success = False
    
    if not test_page_header_formatting():
        success = False
    
    if not test_error_message_format():
        success = False
    
    if success:
        print("\n🎉 All alphanumeric page ID tests passed!")
        print("\n📋 Summary of alphanumeric page ID features:")
        print("   ✅ Generates consistent 4-character alphanumeric IDs")
        print("   ✅ Page headers use alphanumeric IDs instead of numbers")
        print("   ✅ Validation works with alphanumeric IDs")
        print("   ✅ Conversion between IDs and numbers works correctly")
        print("   ✅ Error messages show alphanumeric IDs")
        print("\n🔧 LLM should no longer confuse page IDs with page numbers!")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
