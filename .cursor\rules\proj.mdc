---
description: 
globs: 
alwaysApply: true
---
We are building a world-class, hyper efficient, secure and robust python library for advnaced semantic chunking of documents to enhance Retrieval Augmented Generation (RAG) pipelines. We have description and enhancement documentation in [project_description.md](mdc:project_info/project_description.md) [key_enhancements.md](mdc:project_info/key_enhancements.md) and [enhancement_clarifications.md](mdc:project_info/enhancement_clarifications.md). You must refer to them to have the context needed for this project. We will follow a properly strucutred plan for this project such that we are always aware what has been done and what needs to be done.