#!/usr/bin/env python3
"""
Comprehensive demonstration of all validation and correction features.
"""

from staccato.core.stitcher import StatefulStitcher
from staccato.llm.validation import Event, LLMResponse, validate_page_numbers, validate_fingerprints
from staccato.llm.validation import PageNumberValidationError, FingerprintValidationError


def demo_page_validation():
    """Demonstrate page number validation."""
    print("🎬 DEMO 1: Page Number Validation")
    print("=" * 50)
    
    # Valid page numbers
    print("✅ Testing valid page numbers...")
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section", fingerprint="text"),
        Event(event="ENDS", level="section", page_number=2, fingerprint="text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        validate_page_numbers(response, [1, 2, 3])
        print("   ✅ Valid page numbers passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # Invalid page numbers
    print("\n❌ Testing invalid page numbers...")
    events = [
        Event(event="STARTS", level="section", page_number=99, title="Section", fingerprint="text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        validate_page_numbers(response, [1, 2, 3])
        print("   ❌ Should have failed!")
    except PageNumberValidationError as e:
        print(f"   ✅ Correctly caught invalid page: {e.invalid_pages}")
        print(f"   📝 Error message: {e}")


def demo_fingerprint_validation():
    """Demonstrate fingerprint validation."""
    print("\n\n🎬 DEMO 2: Fingerprint Validation")
    print("=" * 50)
    
    page_content = "This is sample content for testing fingerprints."
    
    # Valid fingerprints
    print("✅ Testing valid fingerprints...")
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section", fingerprint="This is sample"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="testing fingerprints."),
    ]
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    try:
        validate_fingerprints(response, page_content_map)
        print("   ✅ Valid fingerprints passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # Invalid fingerprints
    print("\n❌ Testing invalid fingerprints...")
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section", fingerprint="not found text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        validate_fingerprints(response, page_content_map)
        print("   ❌ Should have failed!")
    except FingerprintValidationError as e:
        print(f"   ✅ Correctly caught missing fingerprint: {e.missing_fingerprints[0]['fingerprint']}")
        print(f"   📝 Error message: {e}")


def demo_gap_detection():
    """Demonstrate gap detection and fixing."""
    print("\n\n🎬 DEMO 3: Gap Detection and Fixing")
    print("=" * 50)
    
    stitcher = StatefulStitcher()
    
    page_content = """Section 1 content here. Section 1 ends here.

This text should belong to Section 1 but was missed by LLM.
More important details that shouldn't be lost.

Section 2 starts here with new content."""
    
    print("📄 Original page content:")
    print(page_content)
    print()
    
    # Events with a gap between ENDS and STARTS
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section 1", fingerprint="Section 1 content"),
        Event(event="ENDS", level="section", page_number=1, fingerprint="Section 1 ends here."),
        Event(event="STARTS", level="section", page_number=1, title="Section 2", fingerprint="Section 2 starts"),
    ]
    
    print("🔍 LLM Events:")
    for event in events:
        print(f"   {event.event} {event.level} on page {event.page_number}: '{event.fingerprint}'")
    print()
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    # Process with gap detection
    stitcher.process_events(llm_response, page_content_map)
    completed_chunks = stitcher.finalize()
    
    print("📊 Results after gap detection:")
    for i, chunk in enumerate(completed_chunks):
        print(f"\n📝 Chunk {i+1} ({chunk.title}):")
        print(f"   Text: '{chunk.text_content.strip()}'")
    
    # Verify gap was fixed
    section1_chunk = completed_chunks[0]
    if "This text should belong to Section 1" in section1_chunk.text_content:
        print("\n✅ Gap text was correctly added to Section 1!")
    else:
        print("\n❌ Gap text was not added properly")


def demo_complete_workflow():
    """Demonstrate the complete validation and correction workflow."""
    print("\n\n🎬 DEMO 4: Complete Workflow")
    print("=" * 50)
    
    print("This demonstrates how all validation layers work together:")
    print()
    print("1️⃣ Page Number Validation")
    print("   - Ensures LLM only references valid pages")
    print("   - Retries with corrective instructions if invalid")
    print()
    print("2️⃣ Fingerprint Validation") 
    print("   - Ensures all fingerprints can be found in page content")
    print("   - Retries with suggestions to use fewer words or check for typos")
    print()
    print("3️⃣ Gap Detection and Fixing")
    print("   - Automatically detects text gaps between ENDS and STARTS")
    print("   - Adds missing text to the appropriate chunk")
    print("   - Ensures no content is lost due to imperfect fingerprints")
    print()
    print("🔄 Retry Logic Flow:")
    print("   LLM Call → Page Validation → Fingerprint Validation → Gap Fixing → Success")
    print("        ↓              ↓                    ↓")
    print("   Add page range  Add fingerprint    Process with")
    print("   correction      suggestions        gap detection")
    print()
    print("📋 Benefits:")
    print("   ✅ Robust error handling and automatic correction")
    print("   ✅ No text loss due to LLM mistakes")
    print("   ✅ Clear feedback to LLM for improvement")
    print("   ✅ Configurable retry limits")
    print("   ✅ Comprehensive logging for debugging")


def demo_error_messages():
    """Show the error messages that would be sent to LLM for correction."""
    print("\n\n🎬 DEMO 5: Error Messages for LLM Correction")
    print("=" * 50)
    
    print("📝 Page Number Error Message:")
    print("   'IMPORTANT CORRECTION: Your previous response contained invalid page numbers: [99, 100].")
    print("    You must ONLY use page numbers 1 through 3 (inclusive).")
    print("    Do not reference any page numbers outside this range.'")
    print()
    
    print("📝 Fingerprint Error Message:")
    print("   'IMPORTANT CORRECTION: Your previous response contained fingerprints that could not be found:")
    print("    ['not found text', 'another missing']. Please check that you are copying the exact text")
    print("    from the document. Try using fewer words (2-3 words maximum) or check for typos in")
    print("    your fingerprints. The fingerprint must appear exactly as written in the page content.'")
    print()
    
    print("📝 Gap Detection (Automatic - No LLM Message):")
    print("   'Gap detected between ENDS and STARTS events - automatically adding missing text")
    print("    to the ending chunk. No LLM correction needed.'")


def main():
    """Run all demonstrations."""
    print("🚀 COMPREHENSIVE VALIDATION AND CORRECTION SYSTEM")
    print("=" * 80)
    print("Demonstrating all validation layers and automatic corrections")
    
    demo_page_validation()
    demo_fingerprint_validation()
    demo_gap_detection()
    demo_complete_workflow()
    demo_error_messages()
    
    print("\n\n🎯 SUMMARY")
    print("=" * 80)
    print("The system now provides three layers of validation and correction:")
    print()
    print("🛡️ LAYER 1: Page Number Validation")
    print("   - Prevents KeyError crashes from invalid page references")
    print("   - Automatic retry with clear page range instructions")
    print()
    print("🛡️ LAYER 2: Fingerprint Validation")
    print("   - Ensures all fingerprints can be found in content")
    print("   - Automatic retry with suggestions for improvement")
    print()
    print("🛡️ LAYER 3: Gap Detection and Fixing")
    print("   - Automatically recovers text missed by imperfect fingerprints")
    print("   - No LLM retry needed - handled transparently")
    print()
    print("🎉 Result: Robust, error-resistant document chunking!")


if __name__ == "__main__":
    main()
