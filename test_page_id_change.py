#!/usr/bin/env python3
"""
Test script to verify the page_id change works correctly.
"""

from staccato.llm.validation import Event, LLMResponse, validate_page_numbers, validate_fingerprints
from staccato.llm.validation import PageNumberValidationError, FingerprintValidationError
from staccato.core.stitcher import Stateful<PERSON>titcher


def test_page_id_validation():
    """Test that page_id validation works correctly."""
    print("🧪 Testing page_id validation...")
    
    # Test valid page_ids
    events = [
        Event(event="STARTS", level="section", page_id=1, title="Section", fingerprint="text"),
        Event(event="ENDS", level="section", page_id=2, fingerprint="text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        validate_page_numbers(response, [1, 2, 3])
        print("   ✅ Valid page_ids passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False
    
    # Test invalid page_ids
    events = [
        Event(event="STARTS", level="section", page_id=99, title="Section", fingerprint="text"),
    ]
    response = LLMResponse(events=events)
    
    try:
        validate_page_numbers(response, [1, 2, 3])
        print("   ❌ Should have failed!")
        return False
    except PageNumberValidationError as e:
        print(f"   ✅ Correctly caught invalid page_id: {e.invalid_pages}")
        assert e.invalid_pages == [99]
    
    return True


def test_fingerprint_validation_with_page_id():
    """Test that fingerprint validation works with page_id."""
    print("\n🧪 Testing fingerprint validation with page_id...")
    
    page_content = "This is sample content for testing."
    
    # Test valid fingerprints
    events = [
        Event(event="STARTS", level="section", page_id=1, title="Section", fingerprint="This is sample"),
    ]
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    try:
        validate_fingerprints(response, page_content_map)
        print("   ✅ Valid fingerprints with page_id passed validation")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False
    
    # Test invalid fingerprints
    events = [
        Event(event="STARTS", level="section", page_id=1, title="Section", fingerprint="not found"),
    ]
    response = LLMResponse(events=events)
    
    try:
        validate_fingerprints(response, page_content_map)
        print("   ❌ Should have failed!")
        return False
    except FingerprintValidationError as e:
        print(f"   ✅ Correctly caught missing fingerprint with page_id: {e.missing_fingerprints[0]['fingerprint']}")
        assert e.page_id == 1
    
    return True


def test_stitcher_with_page_id():
    """Test that the stitcher works correctly with page_id."""
    print("\n🧪 Testing stitcher with page_id...")
    
    stitcher = StatefulStitcher()
    
    page_content = """Section 1 content here. Section 1 ends here.

Section 2 starts here with new content."""
    
    events = [
        Event(event="STARTS", level="section", page_id=1, title="Section 1", fingerprint="Section 1 content"),
        Event(event="ENDS", level="section", page_id=1, fingerprint="Section 1 ends here."),
        Event(event="STARTS", level="section", page_id=1, title="Section 2", fingerprint="Section 2 starts"),
    ]
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    try:
        stitcher.process_events(llm_response, page_content_map)
        completed_chunks = stitcher.finalize()
        
        print(f"   📊 Generated {len(completed_chunks)} chunks")
        
        # Verify chunks were created correctly
        assert len(completed_chunks) == 2
        
        section1 = completed_chunks[0]
        section2 = completed_chunks[1]
        
        print(f"   📝 Section 1: '{section1.text_content.strip()}'")
        print(f"   📝 Section 2: '{section2.text_content.strip()}'")
        
        assert "Section 1 content here. Section 1 ends here." in section1.text_content
        assert "Section 2 starts here with new content." in section2.text_content
        
        # Verify page_id information is preserved
        assert section1.start_page == 1
        assert section1.end_page == 1
        assert section2.start_page == 1
        
        print("   ✅ Stitcher works correctly with page_id")
        return True
        
    except Exception as e:
        print(f"   ❌ Stitcher failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_model():
    """Test that the Event model works with page_id."""
    print("\n🧪 Testing Event model with page_id...")
    
    try:
        # Test creating events with page_id
        event1 = Event(event="STARTS", level="section", page_id=1, title="Test", fingerprint="test text")
        event2 = Event(event="ENDS", level="section", page_id=2, fingerprint="end text")
        event3 = Event(event="CONTINUATION", level="section", page_id=3)
        
        print(f"   📝 STARTS event: page_id={event1.page_id}")
        print(f"   📝 ENDS event: page_id={event2.page_id}")
        print(f"   📝 CONTINUATION event: page_id={event3.page_id}")
        
        # Test creating LLMResponse
        response = LLMResponse(events=[event1, event2, event3])
        
        assert len(response.events) == 3
        assert response.events[0].page_id == 1
        assert response.events[1].page_id == 2
        assert response.events[2].page_id == 3
        
        print("   ✅ Event model works correctly with page_id")
        return True
        
    except Exception as e:
        print(f"   ❌ Event model failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all page_id tests."""
    print("🚀 Testing page_id changes")
    print("=" * 50)
    
    success = True
    
    if not test_event_model():
        success = False
    
    if not test_page_id_validation():
        success = False
    
    if not test_fingerprint_validation_with_page_id():
        success = False
    
    if not test_stitcher_with_page_id():
        success = False
    
    if success:
        print("\n🎉 All page_id tests passed!")
        print("\n📋 Summary of changes:")
        print("   ✅ Event model now uses page_id instead of page_number")
        print("   ✅ Page validation works with page_id")
        print("   ✅ Fingerprint validation works with page_id")
        print("   ✅ Stitcher processes events with page_id correctly")
        print("   ✅ All validation and correction features still work")
        print("\n🔧 The system is ready for the new page_id format!")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
