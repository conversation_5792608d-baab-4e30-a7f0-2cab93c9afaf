[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "staccato"
version = "0.1.0"
authors = [
  { name="<PERSON><PERSON>", email="<EMAIL>" },
]
description = "An advanced, structure-aware chunking engine for RAG pipelines."
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Text Processing :: Indexing",
]
dependencies = [
    "pydantic>=2.0",
    "pydantic-settings",
    "python-dotenv",
    "structlog>=24.0",
    "tenacity",
    "toml",
]

[project.urls]
"Homepage" = "https://github.com/staccato-ai/staccato"
"Issues" = "https://github.com/staccato-ai/staccato/issues"

[project.optional-dependencies]
pdfplumber = ["pdfplumber>=0.10.0"]
pymupdf = ["PyMuPDF>=1.24.0"]
pymupdf4llm = ["pymupdf4llm>=0.0.9"]
pypdf = ["pypdf>=4.0.0"]
docx = ["python-docx>=1.1.0"]
openai = ["openai>=1.12.0", "httpx>=0.27.0"]
all = [
    "staccato[pdfplumber]",
    "staccato[pymupdf]",
    "staccato[pymupdf4llm]",
    "staccato[pypdf]",
    "staccato[docx]",
    "staccato[openai]",
]
dev = [
    "pytest",
    "pytest-asyncio",
    "black",
    "ruff",
]

[tool.setuptools.packages.find]
where = ["src"] 