#!/usr/bin/env python3
"""
Test script to verify markup removal functionality for fingerprint matching.
"""

from staccato.llm.validation import remove_markup_tags, Event, LLMResponse, validate_fingerprints
from staccato.core.stitcher import StatefulStitcher


def test_markup_removal_function():
    """Test the markup removal utility function."""
    print("🧪 Testing markup removal function...")
    
    test_cases = [
        # HTML tags
        ("<p>This is a paragraph</p>", "This is a paragraph"),
        ("<div><span>Nested tags</span></div>", "Nested tags"),
        ("Text with <strong>bold</strong> content", "Text with bold content"),
        
        # Markdown formatting
        ("This is **bold** text", "This is bold text"),
        ("This is *italic* text", "This is italic text"),
        ("Check out [this link](http://example.com)", "Check out this link"),
        
        # Headers
        ("# Main Header\nContent here", "Main Header Content here"),
        ("## Sub Header\nMore content", "Sub Header More content"),
        
        # Code blocks
        ("Text with `inline code` here", "Text with inline code here"),
        ("Before\n```\ncode block\n```\nAfter", "Before After"),
        
        # HTML entities
        ("Text with &amp; symbols &lt;test&gt;", "Text with & symbols <test>"),
        
        # Mixed markup
        ("<p>**Bold** text with <em>emphasis</em></p>", "Bold text with emphasis"),
        
        # Whitespace cleanup
        ("Text   with    extra   spaces", "Text with extra spaces"),
    ]
    
    for input_text, expected in test_cases:
        result = remove_markup_tags(input_text)
        print(f"   Input: '{input_text}'")
        print(f"   Expected: '{expected}'")
        print(f"   Got: '{result}'")
        
        if result == expected:
            print("   ✅ PASS")
        else:
            print("   ❌ FAIL")
            return False
        print()
    
    print("✅ All markup removal tests passed!")
    return True


def test_fingerprint_validation_with_markup():
    """Test fingerprint validation with markup in page content."""
    print("\n🧪 Testing fingerprint validation with markup...")
    
    # Page content with markup tags
    page_content = """<h1>Introduction to Safety</h1>
<p>This is the **introduction** to safety procedures. All employees must follow these guidelines carefully.</p>

<div class="section">
<h2>Safety procedures are complete.</h2>
</div>

<ul>
<li>1. Turn off all equipment immediately</li>
<li>2. Evacuate the building</li>
</ul>"""
    
    print("📄 Page content with markup:")
    print(page_content)
    print()
    
    # LLM sees rendered text without markup, so fingerprints won't have markup
    events = [
        Event(event="STARTS", level="section", page_id=1, title="Safety", fingerprint="introduction to safety"),
        Event(event="ENDS", level="section", page_id=1, fingerprint="procedures are complete."),
        Event(event="STARTS", level="list", page_id=1, title="Steps", fingerprint="1. Turn off"),
    ]
    
    print("🔍 LLM Events (without markup):")
    for event in events:
        print(f"   {event.event}: '{event.fingerprint}'")
    print()
    
    response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    try:
        validate_fingerprints(response, page_content_map)
        print("✅ Fingerprint validation passed with markup removal!")
        return True
    except Exception as e:
        print(f"❌ Fingerprint validation failed: {e}")
        return False


def test_stitcher_with_markup():
    """Test that the stitcher works with markup in page content."""
    print("\n🧪 Testing stitcher with markup...")
    
    stitcher = StatefulStitcher()
    
    # Page content with HTML markup
    page_content = """<div class="section">
<h2>Section 1 content here.</h2>
<p>This section contains important information. <strong>Section 1 ends here.</strong></p>
</div>

<div class="section">
<h2>Section 2 starts here</h2>
<p>This is the beginning of section 2 with new content.</p>
</div>"""
    
    print("📄 Page content with markup:")
    print(page_content)
    print()
    
    # LLM events based on rendered text (without markup)
    events = [
        Event(event="STARTS", level="section", page_id=1, title="Section 1", fingerprint="Section 1 content"),
        Event(event="ENDS", level="section", page_id=1, fingerprint="Section 1 ends here."),
        Event(event="STARTS", level="section", page_id=1, title="Section 2", fingerprint="Section 2 starts"),
    ]
    
    print("🔍 LLM Events:")
    for event in events:
        print(f"   {event.event}: '{event.fingerprint}'")
    print()
    
    llm_response = LLMResponse(events=events)
    page_content_map = {1: page_content}
    
    try:
        stitcher.process_events(llm_response, page_content_map)
        completed_chunks = stitcher.finalize()
        
        print(f"📊 Generated {len(completed_chunks)} chunks")
        
        for i, chunk in enumerate(completed_chunks):
            print(f"\n📝 Chunk {i+1} ({chunk.title}):")
            print(f"   Text: '{chunk.text_content.strip()}'")
        
        # Verify chunks were created correctly
        if len(completed_chunks) == 2:
            print("\n✅ Stitcher successfully processed markup content!")
            return True
        else:
            print(f"\n❌ Expected 2 chunks, got {len(completed_chunks)}")
            return False
            
    except Exception as e:
        print(f"❌ Stitcher failed with markup: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complex_markup_scenarios():
    """Test complex markup scenarios."""
    print("\n🧪 Testing complex markup scenarios...")
    
    # Test nested markup
    complex_text = """
    <div class="container">
        <h1>Main **Title** with `code`</h1>
        <p>Paragraph with <a href="link">linked text</a> and <em>emphasis</em>.</p>
        <blockquote>
            Quote with **bold** and *italic* text.
        </blockquote>
        <pre><code>
        function test() {
            return "code block";
        }
        </code></pre>
    </div>
    """
    
    cleaned = remove_markup_tags(complex_text)
    print(f"📄 Original complex markup:")
    print(complex_text)
    print(f"\n🧹 Cleaned text:")
    print(f"'{cleaned}'")
    
    # Should contain the essential text without markup
    expected_words = ["Main Title with code", "Paragraph with linked text", "emphasis", "Quote with bold and italic"]
    
    success = True
    for word in expected_words:
        if word not in cleaned:
            print(f"❌ Missing expected text: '{word}'")
            success = False
        else:
            print(f"✅ Found expected text: '{word}'")
    
    return success


def main():
    """Run all markup removal tests."""
    print("🚀 Testing markup removal for fingerprint matching")
    print("=" * 60)
    
    success = True
    
    if not test_markup_removal_function():
        success = False
    
    if not test_fingerprint_validation_with_markup():
        success = False
    
    if not test_stitcher_with_markup():
        success = False
    
    if not test_complex_markup_scenarios():
        success = False
    
    if success:
        print("\n🎉 All markup removal tests passed!")
        print("\n📋 Summary of markup removal features:")
        print("   ✅ Removes HTML tags (p, div, span, strong, em, etc.)")
        print("   ✅ Removes Markdown formatting (**bold**, *italic*, [links])")
        print("   ✅ Removes headers (# ## ###)")
        print("   ✅ Removes code blocks (``` and `inline`)")
        print("   ✅ Removes HTML entities (&amp; &lt; &gt;)")
        print("   ✅ Cleans up extra whitespace")
        print("   ✅ Works in both validation and stitcher")
        print("\n🔧 Fingerprint matching is now more robust with markup removal!")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    main()
