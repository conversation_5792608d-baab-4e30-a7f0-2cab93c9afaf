#!/usr/bin/env python3
"""
Test script to verify page number validation functionality.
"""

import pytest
from staccato.llm.validation import Event, LLMResponse, validate_page_numbers, PageNumberValidationError


def test_valid_page_numbers():
    """Test that validation passes with valid page numbers."""
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_number=2, fingerprint="text2"),
        Event(event="CONTINUATION", level="section", page_number=3),
    ]
    response = LLMResponse(events=events)
    valid_pages = [1, 2, 3]
    
    # Should not raise any exception
    validate_page_numbers(response, valid_pages)


def test_invalid_page_numbers():
    """Test that validation fails with invalid page numbers."""
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_number=5, fingerprint="text2"),  # Invalid page
        Event(event="CONTINUATION", level="section", page_number=10),  # Invalid page
    ]
    response = LLMResponse(events=events)
    valid_pages = [1, 2, 3]
    
    with pytest.raises(PageNumberValidationError) as exc_info:
        validate_page_numbers(response, valid_pages)
    
    error = exc_info.value
    assert error.invalid_pages == [5, 10]
    assert error.valid_range == (1, 3)
    assert "invalid page numbers: [5, 10]" in str(error)
    assert "Valid page range is 1-3" in str(error)


def test_empty_events():
    """Test validation with empty events list."""
    response = LLMResponse(events=[])
    valid_pages = [1, 2, 3]
    
    # Should not raise any exception
    validate_page_numbers(response, valid_pages)


def test_empty_valid_pages():
    """Test validation with empty valid pages list."""
    events = [
        Event(event="STARTS", level="section", page_number=1, title="Section 1", fingerprint="text1"),
    ]
    response = LLMResponse(events=events)
    valid_pages = []
    
    # Should not raise any exception (early return)
    validate_page_numbers(response, valid_pages)


def test_duplicate_invalid_pages():
    """Test that duplicate invalid page numbers are handled correctly."""
    events = [
        Event(event="STARTS", level="section", page_number=5, title="Section 1", fingerprint="text1"),
        Event(event="ENDS", level="section", page_number=5, fingerprint="text2"),  # Same invalid page
        Event(event="CONTINUATION", level="section", page_number=10),  # Different invalid page
    ]
    response = LLMResponse(events=events)
    valid_pages = [1, 2, 3]
    
    with pytest.raises(PageNumberValidationError) as exc_info:
        validate_page_numbers(response, valid_pages)
    
    error = exc_info.value
    # Should remove duplicates and sort
    assert error.invalid_pages == [5, 10]


if __name__ == "__main__":
    print("Running page validation tests...")
    
    try:
        test_valid_page_numbers()
        print("✓ test_valid_page_numbers passed")
        
        test_invalid_page_numbers()
        print("✓ test_invalid_page_numbers passed")
        
        test_empty_events()
        print("✓ test_empty_events passed")
        
        test_empty_valid_pages()
        print("✓ test_empty_valid_pages passed")
        
        test_duplicate_invalid_pages()
        print("✓ test_duplicate_invalid_pages passed")
        
        print("\nAll tests passed! ✅")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
